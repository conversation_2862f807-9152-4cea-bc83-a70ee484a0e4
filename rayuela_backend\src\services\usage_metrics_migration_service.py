"""
Servicio para migrar métricas de uso de Subscription a AccountUsageMetrics.

Este servicio maneja la consolidación de métricas de uso para eliminar redundancia
entre las tablas Subscription y AccountUsageMetrics.
"""

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import update
from typing import Dict, Any, List
from datetime import datetime, timezone

from src.db.models.subscription import Subscription
from src.db.models.account_usage_metrics import AccountUsageMetrics
from src.utils.base_logger import log_info, log_error, log_warning


class UsageMetricsMigrationService:
    """Servicio para migrar métricas de uso entre modelos."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def migrate_subscription_metrics_to_usage_metrics(self) -> Dict[str, Any]:
        """
        Migra las métricas de uso de Subscription a AccountUsageMetrics.
        
        Returns:
            Dict con estadísticas de la migración
        """
        try:
            migration_stats = {
                "accounts_processed": 0,
                "accounts_migrated": 0,
                "accounts_updated": 0,
                "accounts_skipped": 0,
                "errors": []
            }
            
            # Obtener todas las suscripciones con métricas de uso
            stmt = select(Subscription).where(
                (Subscription.monthly_api_calls_used > 0) | 
                (Subscription.storage_used > 0) |
                (Subscription.last_reset_date.isnot(None))
            )
            result = await self.db.execute(stmt)
            subscriptions = result.scalars().all()
            
            log_info(f"Found {len(subscriptions)} subscriptions with usage metrics to migrate")
            
            for subscription in subscriptions:
                migration_stats["accounts_processed"] += 1
                
                try:
                    await self._migrate_single_account_metrics(subscription, migration_stats)
                except Exception as e:
                    error_msg = f"Error migrating account {subscription.account_id}: {str(e)}"
                    log_error(error_msg)
                    migration_stats["errors"].append(error_msg)
            
            # Commit todas las migraciones
            await self.db.commit()
            
            log_info(f"Migration completed: {migration_stats}")
            return migration_stats
            
        except Exception as e:
            await self.db.rollback()
            log_error(f"Migration failed: {str(e)}")
            raise
    
    async def _migrate_single_account_metrics(
        self, 
        subscription: Subscription, 
        stats: Dict[str, Any]
    ) -> None:
        """Migra las métricas de una sola cuenta."""
        
        # Verificar si ya existe AccountUsageMetrics para esta cuenta
        stmt = select(AccountUsageMetrics).where(
            AccountUsageMetrics.account_id == subscription.account_id
        )
        result = await self.db.execute(stmt)
        existing_metrics = result.scalars().first()
        
        if existing_metrics:
            # Actualizar métricas existentes
            await self._update_existing_metrics(subscription, existing_metrics)
            stats["accounts_updated"] += 1
        else:
            # Crear nuevas métricas
            await self._create_new_metrics(subscription)
            stats["accounts_migrated"] += 1
    
    async def _update_existing_metrics(
        self, 
        subscription: Subscription, 
        metrics: AccountUsageMetrics
    ) -> None:
        """Actualiza métricas existentes con datos de suscripción."""
        
        # Solo actualizar si los valores de suscripción son mayores
        # (para evitar sobrescribir datos más recientes)
        update_values = {}
        
        if subscription.monthly_api_calls_used > metrics.billing_period_api_calls:
            update_values["billing_period_api_calls"] = subscription.monthly_api_calls_used
            log_info(f"Updating API calls for account {subscription.account_id}: "
                    f"{metrics.billing_period_api_calls} -> {subscription.monthly_api_calls_used}")
        
        if subscription.storage_used > metrics.billing_period_storage:
            update_values["billing_period_storage"] = subscription.storage_used
            log_info(f"Updating storage for account {subscription.account_id}: "
                    f"{metrics.billing_period_storage} -> {subscription.storage_used}")
        
        if subscription.last_reset_date and (
            not metrics.last_billing_cycle or 
            subscription.last_reset_date > metrics.last_billing_cycle
        ):
            update_values["last_billing_cycle"] = subscription.last_reset_date
        
        if update_values:
            update_values["updated_at"] = datetime.now(timezone.utc)
            
            stmt = (
                update(AccountUsageMetrics)
                .where(AccountUsageMetrics.account_id == subscription.account_id)
                .values(**update_values)
            )
            await self.db.execute(stmt)
    
    async def _create_new_metrics(self, subscription: Subscription) -> None:
        """Crea nuevas métricas basadas en datos de suscripción."""
        
        new_metrics = AccountUsageMetrics(
            account_id=subscription.account_id,
            billing_period_api_calls=subscription.monthly_api_calls_used,
            billing_period_storage=subscription.storage_used,
            last_billing_cycle=subscription.last_reset_date or datetime.now(timezone.utc),
            total_api_calls_lifetime=subscription.monthly_api_calls_used,  # Asumir que es el total
            total_storage_lifetime=subscription.storage_used,
            updated_at=datetime.now(timezone.utc)
        )
        
        self.db.add(new_metrics)
        log_info(f"Created new usage metrics for account {subscription.account_id}")
    
    async def verify_migration(self) -> Dict[str, Any]:
        """
        Verifica que la migración se haya completado correctamente.
        
        Returns:
            Dict con estadísticas de verificación
        """
        try:
            # Contar suscripciones con métricas
            stmt = select(Subscription).where(
                (Subscription.monthly_api_calls_used > 0) | 
                (Subscription.storage_used > 0)
            )
            result = await self.db.execute(stmt)
            subscriptions_with_metrics = len(result.scalars().all())
            
            # Contar AccountUsageMetrics
            stmt = select(AccountUsageMetrics)
            result = await self.db.execute(stmt)
            usage_metrics_count = len(result.scalars().all())
            
            # Verificar consistencia
            inconsistencies = await self._find_inconsistencies()
            
            verification_result = {
                "subscriptions_with_metrics": subscriptions_with_metrics,
                "usage_metrics_records": usage_metrics_count,
                "inconsistencies_found": len(inconsistencies),
                "inconsistencies": inconsistencies,
                "migration_complete": len(inconsistencies) == 0
            }
            
            log_info(f"Migration verification: {verification_result}")
            return verification_result
            
        except Exception as e:
            log_error(f"Verification failed: {str(e)}")
            raise
    
    async def _find_inconsistencies(self) -> List[Dict[str, Any]]:
        """Encuentra inconsistencias entre Subscription y AccountUsageMetrics."""
        
        inconsistencies = []
        
        # Obtener todas las suscripciones con métricas
        stmt = select(Subscription).where(
            (Subscription.monthly_api_calls_used > 0) | 
            (Subscription.storage_used > 0)
        )
        result = await self.db.execute(stmt)
        subscriptions = result.scalars().all()
        
        for subscription in subscriptions:
            # Obtener métricas correspondientes
            stmt = select(AccountUsageMetrics).where(
                AccountUsageMetrics.account_id == subscription.account_id
            )
            result = await self.db.execute(stmt)
            metrics = result.scalars().first()
            
            if not metrics:
                inconsistencies.append({
                    "account_id": subscription.account_id,
                    "issue": "Missing AccountUsageMetrics record",
                    "subscription_api_calls": subscription.monthly_api_calls_used,
                    "subscription_storage": subscription.storage_used
                })
            elif (
                subscription.monthly_api_calls_used > metrics.billing_period_api_calls or
                subscription.storage_used > metrics.billing_period_storage
            ):
                inconsistencies.append({
                    "account_id": subscription.account_id,
                    "issue": "Subscription metrics higher than AccountUsageMetrics",
                    "subscription_api_calls": subscription.monthly_api_calls_used,
                    "metrics_api_calls": metrics.billing_period_api_calls,
                    "subscription_storage": subscription.storage_used,
                    "metrics_storage": metrics.billing_period_storage
                })
        
        return inconsistencies
    
    async def cleanup_deprecated_fields(self) -> Dict[str, Any]:
        """
        Limpia los campos deprecados de Subscription después de verificar la migración.
        
        ADVERTENCIA: Esta operación es irreversible. Solo ejecutar después de verificar
        que la migración fue exitosa y que todos los servicios usan AccountUsageMetrics.
        
        Returns:
            Dict con estadísticas de limpieza
        """
        try:
            # Verificar que la migración esté completa
            verification = await self.verify_migration()
            if not verification["migration_complete"]:
                raise ValueError(
                    f"Migration not complete. Found {verification['inconsistencies_found']} inconsistencies"
                )
            
            # Resetear campos deprecados a 0
            stmt = (
                update(Subscription)
                .values(
                    monthly_api_calls_used=0,
                    storage_used=0,
                    last_reset_date=None
                )
            )
            result = await self.db.execute(stmt)
            
            await self.db.commit()
            
            cleanup_stats = {
                "subscriptions_cleaned": result.rowcount,
                "cleanup_completed": True
            }
            
            log_info(f"Cleanup completed: {cleanup_stats}")
            return cleanup_stats
            
        except Exception as e:
            await self.db.rollback()
            log_error(f"Cleanup failed: {str(e)}")
            raise
