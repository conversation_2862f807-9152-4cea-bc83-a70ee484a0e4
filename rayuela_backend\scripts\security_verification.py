#!/usr/bin/env python3
"""
Script de verificación de seguridad para Rayuela.

Este script verifica que todas las mitigaciones de seguridad estén correctamente implementadas
y funcionando según el informe de seguridad.
"""

import os
import sys
import asyncio
import subprocess
from typing import List, Dict, Any
from pathlib import Path

# Agregar src al path para imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

def print_header(title: str):
    """Imprime un header formateado."""
    print(f"\n{'='*60}")
    print(f"🛡️  {title}")
    print(f"{'='*60}")

def print_check(description: str, status: bool, details: str = ""):
    """Imprime el resultado de una verificación."""
    icon = "✅" if status else "❌"
    print(f"{icon} {description}")
    if details:
        print(f"   {details}")

def print_warning(message: str):
    """Imprime una advertencia."""
    print(f"⚠️  {message}")

def print_info(message: str):
    """Imprime información."""
    print(f"ℹ️  {message}")

class SecurityVerifier:
    """Verificador de seguridad para Rayuela."""
    
    def __init__(self):
        self.checks_passed = 0
        self.checks_failed = 0
        self.warnings = 0

    def verify_idor_mitigation(self) -> bool:
        """Verifica que la vulnerabilidad IDOR en list_accounts esté mitigada."""
        print_header("Verificación: Mitigación IDOR en list_accounts")
        
        try:
            # Verificar que el endpoint requiere autorización de admin
            from src.api.v1.endpoints.accounts import list_accounts
            import inspect
            
            # Obtener la signatura de la función
            sig = inspect.signature(list_accounts)
            
            # Verificar que tiene dependencia de get_current_admin_user
            has_admin_dep = any(
                "get_current_admin_user" in str(param.default) 
                for param in sig.parameters.values()
            )
            
            print_check(
                "Endpoint list_accounts requiere autorización de administrador",
                has_admin_dep,
                "Dependencia get_current_admin_user encontrada" if has_admin_dep else "FALTA dependencia de autorización"
            )
            
            if has_admin_dep:
                self.checks_passed += 1
                return True
            else:
                self.checks_failed += 1
                return False
                
        except Exception as e:
            print_check("Error verificando mitigación IDOR", False, str(e))
            self.checks_failed += 1
            return False

    def verify_cors_configuration(self) -> bool:
        """Verifica que la configuración CORS sea segura."""
        print_header("Verificación: Configuración CORS")
        
        try:
            from src.core.config import get_allowed_origins
            
            # Test en producción
            original_env = os.environ.get("ENV")
            os.environ["ENV"] = "production"
            os.environ.pop("ALLOWED_ORIGINS", None)  # Limpiar para test
            
            try:
                prod_origins = get_allowed_origins()
                
                # Verificar que no hay wildcards
                has_wildcards = "*" in prod_origins
                print_check(
                    "No hay wildcards en configuración de producción",
                    not has_wildcards,
                    f"Orígenes: {prod_origins}" if not has_wildcards else "ENCONTRADO wildcard '*'"
                )
                
                # Verificar HTTPS en producción
                all_https = all(origin.startswith("https://") for origin in prod_origins if origin)
                print_check(
                    "Todos los orígenes de producción usan HTTPS",
                    all_https,
                    f"Orígenes verificados: {len(prod_origins)}"
                )
                
                success = not has_wildcards and all_https
                
            finally:
                # Restaurar ENV original
                if original_env:
                    os.environ["ENV"] = original_env
                else:
                    os.environ.pop("ENV", None)
            
            if success:
                self.checks_passed += 1
            else:
                self.checks_failed += 1
                
            return success
            
        except Exception as e:
            print_check("Error verificando configuración CORS", False, str(e))
            self.checks_failed += 1
            return False

    def verify_security_headers(self) -> bool:
        """Verifica que los headers de seguridad estén implementados."""
        print_header("Verificación: Headers de Seguridad HTTP")
        
        try:
            # Verificar que el middleware de headers existe
            from src.middleware.setup import setup_middleware
            
            # Crear una app de prueba para verificar headers
            from fastapi import FastAPI
            from fastapi.testclient import TestClient
            
            test_app = FastAPI()
            setup_middleware(test_app)
            
            @test_app.get("/test")
            async def test_endpoint():
                return {"test": "ok"}
            
            client = TestClient(test_app)
            response = client.get("/test")
            
            # Verificar headers básicos
            required_headers = {
                "X-Content-Type-Options": "nosniff",
                "X-Frame-Options": "DENY",
                "Referrer-Policy": "strict-origin-when-cross-origin"
            }
            
            all_present = True
            for header, expected_value in required_headers.items():
                actual_value = response.headers.get(header)
                is_present = actual_value == expected_value
                print_check(
                    f"Header {header}",
                    is_present,
                    f"Valor: {actual_value}" if is_present else f"Esperado: {expected_value}, Actual: {actual_value}"
                )
                if not is_present:
                    all_present = False
            
            # Verificar que headers reveladores están removidos
            removed_headers = ["Server", "X-Powered-By"]
            headers_removed = True
            for header in removed_headers:
                is_removed = header not in response.headers
                print_check(
                    f"Header {header} removido",
                    is_removed,
                    "Correctamente removido" if is_removed else f"PRESENTE: {response.headers.get(header)}"
                )
                if not is_removed:
                    headers_removed = False
            
            success = all_present and headers_removed
            
            if success:
                self.checks_passed += 1
            else:
                self.checks_failed += 1
                
            return success
            
        except Exception as e:
            print_check("Error verificando headers de seguridad", False, str(e))
            self.checks_failed += 1
            return False

    def verify_temp_backend_controls(self) -> bool:
        """Verifica que los controles del modo temporal backend funcionen."""
        print_header("Verificación: Controles Modo Temporal Backend")
        
        try:
            from src.main import _is_temp_backend_safe
            
            # Test 1: Debe estar deshabilitado en producción
            original_env = dict(os.environ)
            
            try:
                os.environ.update({
                    "ENV": "production",
                    "TEMP_BACKEND_MODE": "true"
                })
                
                prod_safe = not _is_temp_backend_safe()
                print_check(
                    "Modo temporal deshabilitado en producción",
                    prod_safe,
                    "Correctamente bloqueado" if prod_safe else "PELIGRO: Permitido en producción"
                )
                
                # Test 2: Debe estar deshabilitado con indicadores de producción
                os.environ.update({
                    "ENV": "development",
                    "TEMP_BACKEND_MODE": "true",
                    "GCP_PROJECT_ID": "production-project"
                })
                
                indicator_safe = not _is_temp_backend_safe()
                print_check(
                    "Modo temporal deshabilitado con indicadores de producción",
                    indicator_safe,
                    "Correctamente bloqueado" if indicator_safe else "PELIGRO: Permitido con indicadores de prod"
                )
                
                success = prod_safe and indicator_safe
                
            finally:
                # Restaurar variables de entorno
                os.environ.clear()
                os.environ.update(original_env)
            
            if success:
                self.checks_passed += 1
            else:
                self.checks_failed += 1
                
            return success
            
        except Exception as e:
            print_check("Error verificando controles de modo temporal", False, str(e))
            self.checks_failed += 1
            return False

    def verify_database_optimizations(self) -> bool:
        """Verifica que las optimizaciones de base de datos estén implementadas."""
        print_header("Verificación: Optimizaciones de Base de Datos")
        
        try:
            # Verificar que las tareas usan get_async_session
            from src.utils.audit_tasks import write_audit_log_to_db_task
            from src.middleware.audit import AuditMiddleware
            
            import inspect
            
            # Verificar audit_tasks.py
            audit_task_source = inspect.getsource(write_audit_log_to_db_task)
            uses_async_session = "get_async_session" in audit_task_source
            print_check(
                "Tareas de auditoría usan get_async_session",
                uses_async_session,
                "Optimización implementada" if uses_async_session else "Usando método menos eficiente"
            )
            
            # Verificar middleware de auditoría
            middleware_source = inspect.getsource(AuditMiddleware._track_api_analytics)
            middleware_optimized = "get_async_session" in middleware_source
            print_check(
                "Middleware de analytics usa get_async_session",
                middleware_optimized,
                "Optimización implementada" if middleware_optimized else "Usando método menos eficiente"
            )
            
            success = uses_async_session and middleware_optimized
            
            if success:
                self.checks_passed += 1
            else:
                self.checks_failed += 1
                
            return success
            
        except Exception as e:
            print_check("Error verificando optimizaciones de BD", False, str(e))
            self.checks_failed += 1
            return False

    def run_security_tests(self) -> bool:
        """Ejecuta los tests de seguridad."""
        print_header("Ejecución de Tests de Seguridad")
        
        try:
            # Ejecutar tests específicos de seguridad
            test_file = Path(__file__).parent.parent / "tests" / "security" / "test_security_mitigations.py"
            
            if not test_file.exists():
                print_check("Tests de seguridad encontrados", False, f"Archivo no encontrado: {test_file}")
                self.checks_failed += 1
                return False
            
            # Ejecutar pytest en el archivo de tests de seguridad
            result = subprocess.run([
                sys.executable, "-m", "pytest", 
                str(test_file), 
                "-v", "--tb=short"
            ], capture_output=True, text=True, cwd=Path(__file__).parent.parent)
            
            tests_passed = result.returncode == 0
            print_check(
                "Tests de seguridad ejecutados",
                tests_passed,
                f"Código de salida: {result.returncode}"
            )
            
            if not tests_passed and result.stdout:
                print("📋 Salida de tests:")
                print(result.stdout)
            
            if not tests_passed and result.stderr:
                print("❌ Errores:")
                print(result.stderr)
            
            if tests_passed:
                self.checks_passed += 1
            else:
                self.checks_failed += 1
                
            return tests_passed
            
        except Exception as e:
            print_check("Error ejecutando tests de seguridad", False, str(e))
            self.checks_failed += 1
            return False

    def generate_report(self):
        """Genera un reporte final de la verificación."""
        print_header("Reporte Final de Seguridad")
        
        total_checks = self.checks_passed + self.checks_failed
        success_rate = (self.checks_passed / total_checks * 100) if total_checks > 0 else 0
        
        print(f"📊 Verificaciones totales: {total_checks}")
        print(f"✅ Verificaciones exitosas: {self.checks_passed}")
        print(f"❌ Verificaciones fallidas: {self.checks_failed}")
        print(f"⚠️  Advertencias: {self.warnings}")
        print(f"📈 Tasa de éxito: {success_rate:.1f}%")
        
        if self.checks_failed == 0:
            print("\n🎉 ¡Todas las mitigaciones de seguridad están correctamente implementadas!")
            return True
        else:
            print(f"\n🚨 Se encontraron {self.checks_failed} problemas de seguridad que requieren atención.")
            return False

def main():
    """Función principal del verificador de seguridad."""
    print_header("Verificador de Seguridad - Rayuela")
    print("Verificando implementación de mitigaciones del informe de seguridad...")
    
    verifier = SecurityVerifier()
    
    # Ejecutar todas las verificaciones
    verifier.verify_idor_mitigation()
    verifier.verify_cors_configuration()
    verifier.verify_security_headers()
    verifier.verify_temp_backend_controls()
    verifier.verify_database_optimizations()
    verifier.run_security_tests()
    
    # Generar reporte final
    success = verifier.generate_report()
    
    # Código de salida
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
