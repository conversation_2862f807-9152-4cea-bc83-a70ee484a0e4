"""
Tests de seguridad para verificar las mitigaciones implementadas.

Este archivo contiene tests específicos para verificar que las vulnerabilidades
identificadas en el informe de seguridad han sido correctamente mitigadas.
"""

import pytest
import os
from unittest.mock import patch, AsyncMock
from fastapi.testclient import TestClient
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from src.main import app
from src.db.models.system_user import SystemUser
from src.db.models.account import Account


class TestSecurityMitigations:
    """Tests para verificar las mitigaciones de seguridad implementadas."""

    @pytest.mark.asyncio
    async def test_list_accounts_requires_admin_authorization(self, async_client: AsyncClient):
        """
        Test: Verificar que el endpoint list_accounts requiere autorización de administrador.
        
        Mitiga: Vulnerabilidad IDOR vertical en GET /api/v1/accounts/
        """
        # Test 1: Sin autenticación debe retornar 401
        response = await async_client.get("/api/v1/accounts/")
        assert response.status_code == 401
        
        # Test 2: Con usuario no-admin debe retornar 403
        # (Este test requiere mock de usuario autenticado pero no admin)
        with patch('src.core.deps.get_current_admin_user') as mock_admin:
            mock_admin.side_effect = Exception("Insufficient permissions")
            response = await async_client.get("/api/v1/accounts/")
            assert response.status_code in [401, 403, 500]  # Cualquiera de estos es aceptable

    def test_cors_configuration_security(self):
        """
        Test: Verificar que la configuración CORS es segura.
        
        Mitiga: Configuración CORS permisiva
        """
        from src.core.config import get_allowed_origins
        
        # Test 1: En producción no debe haber wildcards
        with patch.dict(os.environ, {"ENV": "production", "ALLOWED_ORIGINS": ""}):
            origins = get_allowed_origins()
            assert "*" not in origins, "Wildcard origins not allowed in production"
            assert all(origin.startswith("https://") for origin in origins if origin), \
                "All production origins should use HTTPS"

        # Test 2: Verificar que la función maneja correctamente entornos de desarrollo
        with patch.dict(os.environ, {"ENV": "development"}, clear=False):
            origins = get_allowed_origins()
            assert isinstance(origins, list), "Origins should be a list"

    def test_cors_wildcard_prevention_in_production(self):
        """
        Test: Verificar que se previene el uso de wildcards en producción.
        """
        from src.middleware.setup import setup_middleware
        
        # Simular configuración de producción con wildcard (debe fallar)
        with patch.dict(os.environ, {"ENV": "production"}):
            with patch('src.core.config.get_allowed_origins', return_value=["*"]):
                test_app = TestClient(app)
                # El middleware debe detectar y prevenir wildcards en producción
                # Esto se verifica en el setup del middleware
                try:
                    setup_middleware(test_app.app)
                    assert False, "Should have raised ValueError for wildcard in production"
                except ValueError as e:
                    assert "wildcard" in str(e).lower()

    def test_security_headers_implementation(self):
        """
        Test: Verificar que los headers de seguridad HTTP están implementados.
        
        Mitiga: Ausencia de headers de seguridad HTTP
        """
        client = TestClient(app)
        response = client.get("/health")
        
        # Verificar headers básicos de seguridad
        assert response.headers.get("X-Content-Type-Options") == "nosniff"
        assert response.headers.get("X-Frame-Options") == "DENY"
        assert response.headers.get("Referrer-Policy") == "strict-origin-when-cross-origin"
        
        # Verificar que headers reveladores han sido removidos
        assert "Server" not in response.headers
        assert "X-Powered-By" not in response.headers

    def test_security_headers_production_vs_development(self):
        """
        Test: Verificar que los headers de seguridad son más estrictos en producción.
        """
        client = TestClient(app)
        
        # Test en modo desarrollo
        with patch.dict(os.environ, {"ENV": "development"}):
            response = client.get("/health")
            csp = response.headers.get("Content-Security-Policy", "")
            assert "unsafe-inline" in csp or "unsafe-eval" in csp, \
                "Development should have more permissive CSP"

        # Test en modo producción
        with patch.dict(os.environ, {"ENV": "production"}):
            response = client.get("/health")
            
            # Verificar HSTS en producción
            hsts = response.headers.get("Strict-Transport-Security")
            assert hsts is not None, "HSTS should be present in production"
            assert "max-age=" in hsts
            
            # Verificar CSP más estricto en producción
            csp = response.headers.get("Content-Security-Policy", "")
            assert "unsafe-inline" not in csp or "'unsafe-inline'" not in csp.split("script-src")[0], \
                "Production CSP should be more restrictive"

    def test_temp_backend_mode_security_controls(self):
        """
        Test: Verificar que el modo temporal backend tiene controles de seguridad.
        
        Mitiga: Activación accidental del modo temporal en producción
        """
        # Test 1: Modo temporal debe estar deshabilitado en producción
        with patch.dict(os.environ, {"ENV": "production", "TEMP_BACKEND_MODE": "true"}):
            from src.main import _is_temp_backend_safe
            assert not _is_temp_backend_safe(), \
                "Temp backend mode should be disabled in production"

        # Test 2: Modo temporal debe estar deshabilitado con indicadores de producción
        with patch.dict(os.environ, {
            "ENV": "development", 
            "TEMP_BACKEND_MODE": "true",
            "GCP_PROJECT_ID": "production-project"
        }):
            from src.main import _is_temp_backend_safe
            assert not _is_temp_backend_safe(), \
                "Temp backend mode should be disabled with production indicators"

        # Test 3: Modo temporal debe funcionar solo en desarrollo limpio
        with patch.dict(os.environ, {
            "ENV": "development", 
            "TEMP_BACKEND_MODE": "true"
        }, clear=True):
            from src.main import _is_temp_backend_safe
            assert _is_temp_backend_safe(), \
                "Temp backend mode should work in clean development environment"

    def test_temp_backend_health_endpoint_warning(self):
        """
        Test: Verificar que el endpoint de salud advierte sobre el modo temporal.
        """
        # Simular modo temporal
        with patch('src.main.IS_TEMP_BACKEND', True):
            client = TestClient(app)
            response = client.get("/health")
            
            assert response.status_code == 200
            data = response.json()
            assert data["mode"] == "temporary"
            assert "warning" in data
            assert "TEMPORARY MODE" in data["warning"]
            assert "security_note" in data

    @pytest.mark.asyncio
    async def test_database_session_optimization(self):
        """
        Test: Verificar que las sesiones de base de datos se manejan eficientemente.
        
        Mitiga: Agotamiento del pool de conexiones en tareas en segundo plano
        """
        from src.utils.audit_tasks import write_audit_log_to_db_task
        from src.db.session import get_async_session
        
        # Mock de datos de auditoría
        audit_data = {
            "account_id": 1,
            "method": "GET",
            "url": "/test",
            "status_code": 200,
            "process_time": 0.1,
            "client_ip": "127.0.0.1",
            "user_agent": "test"
        }
        
        # Verificar que la función usa get_async_session correctamente
        with patch('src.utils.audit_tasks.get_async_session') as mock_session:
            mock_db = AsyncMock()
            mock_session.return_value.__aenter__.return_value = mock_db
            mock_db.begin.return_value.__aenter__ = AsyncMock()
            mock_db.begin.return_value.__aexit__ = AsyncMock()
            
            with patch('src.utils.audit_tasks.write_audit_log_to_db') as mock_write:
                await write_audit_log_to_db_task(audit_data)
                
                # Verificar que se usó get_async_session
                mock_session.assert_called_once()
                # Verificar que se inició una transacción
                mock_db.begin.assert_called_once()

    def test_api_endpoint_cache_headers(self):
        """
        Test: Verificar que los endpoints sensibles tienen headers de cache apropiados.
        """
        client = TestClient(app)
        
        # Test endpoint API (sensible)
        response = client.get("/api/v1/health")  # Asumiendo que existe
        if response.status_code != 404:  # Solo si el endpoint existe
            cache_control = response.headers.get("Cache-Control", "")
            assert "no-store" in cache_control or "no-cache" in cache_control, \
                "API endpoints should have no-cache headers"

    def test_environment_based_security_configuration(self):
        """
        Test: Verificar que la configuración de seguridad se adapta al entorno.
        """
        # Test configuración de desarrollo
        with patch.dict(os.environ, {"ENV": "development"}):
            from src.core.config import get_allowed_origins
            dev_origins = get_allowed_origins()
            assert any("localhost" in origin for origin in dev_origins), \
                "Development should allow localhost"

        # Test configuración de producción
        with patch.dict(os.environ, {"ENV": "production", "FRONTEND_URL": "https://app.rayuela.com"}):
            from src.core.config import get_allowed_origins
            prod_origins = get_allowed_origins()
            assert all(origin.startswith("https://") for origin in prod_origins if origin), \
                "Production should only allow HTTPS origins"
