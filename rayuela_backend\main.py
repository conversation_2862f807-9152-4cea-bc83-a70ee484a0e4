# main.py
import os
from fastapi import FastAPI, Depends
from contextlib import asynccontextmanager

# Check if this is a temporary backend deployment (lightweight mode)
# SECURITY: Multiple safeguards to prevent accidental activation in production
def _is_temp_backend_safe() -> bool:
    """
    Determina si es seguro activar el modo temporal backend.

    CONTROLES DE SEGURIDAD:
    1. Variable TEMP_BACKEND_MODE debe ser explícitamente "true"
    2. ENV no debe ser "production"
    3. Verificación adicional de variables de producción
    4. Log de advertencia si se intenta activar
    """
    temp_mode_requested = os.getenv("TEMP_BACKEND_MODE", "false").lower() == "true"
    env = os.getenv("ENV", "development")

    # Control 1: ENV no debe ser production
    if env == "production":
        if temp_mode_requested:
            print("🚨 SECURITY WARNING: TEMP_BACKEND_MODE requested in production - DENIED")
        return False

    # Control 2: Verificar variables típicas de producción
    production_indicators = [
        os.getenv("GCP_PROJECT_ID"),
        os.getenv("CLOUD_SQL_CONNECTION_NAME"),
        os.getenv("FRONTEND_URL", "").startswith("https://")
    ]

    if any(production_indicators) and temp_mode_requested:
        print("🚨 SECURITY WARNING: TEMP_BACKEND_MODE requested in production-like environment - DENIED")
        return False

    # Control 3: Log si se activa el modo temporal
    if temp_mode_requested:
        print("⚠️ TEMP_BACKEND_MODE activated - OpenAPI generation mode only")
        print(f"   Environment: {env}")
        print("   This mode should NEVER be used in production")

    return temp_mode_requested

IS_TEMP_BACKEND = _is_temp_backend_safe()

if IS_TEMP_BACKEND:
    # Ultra-lightweight configuration for temporary backend
    from pydantic_settings import BaseSettings
    
    class LightweightSettings(BaseSettings):
        PROJECT_NAME: str = "Rayuela (Temp)"
        API_VERSION: str = "v1"
        ENV: str = os.getenv("ENV", "development")
        API_HOST: str = os.getenv("API_HOST", "0.0.0.0")
        API_PORT: int = int(os.getenv("API_PORT", "8080"))
        
        # Minimal required settings for OpenAPI generation
        SECRET_KEY: str = "temp-backend-key-not-for-production"
        
        def load_secrets_from_gcp(self):
            return True  # Dummy implementation
    
    settings = LightweightSettings()
    
    # Lightweight imports for temp mode
    from src.api.v1.api import public_router, private_router
    
    # Create a simple logger for temp mode
    import logging
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.INFO)
    
    # Simple dependency for temp mode (no auth)
    def get_current_account():
        return None
else:
    # Full configuration for normal operation
    from src.core.config import settings
    from src.api.v1.api import public_router, private_router
    from src.api.v1.endpoints.health import router as health_router  # Endpoint de salud simple
    from src.utils.base_logger import logger
    from src.core.deps import get_current_account

# Only import heavy dependencies if not in temp mode
if not IS_TEMP_BACKEND:
    from src.middleware.setup import setup_middleware
    from src.core.error_handlers import register_exception_handlers
    from src.utils.maintenance import cleanup_old_audit_logs  # Mantenimiento
    from src.db.session import init_db  # Asumiendo que init_db crea tablas si no existen

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    logger.info(f"Starting up Rayuela - ENV: {settings.ENV}")
    
    if IS_TEMP_BACKEND:
        logger.info("🚀 TEMPORARY BACKEND MODE - Ultra-lightweight startup")
        logger.info("⏭️  Skipping all heavy initialization for OpenAPI generation")
    else:
        # Cargar secretos de GCP en producción (skip si se especifica)
        skip_secrets = os.getenv("SKIP_SECRETS", "false").lower() == "true"
        if settings.ENV == "production" and not skip_secrets:
            logger.info("🔐 Cargando secretos desde GCP Secret Manager...")
            try:
                result = settings.load_secrets_from_gcp()
                if result:
                    logger.info("✅ Secretos cargados correctamente desde Secret Manager")
                    logger.info("🔒 Configuración de infraestructura protegida")
                else:
                    logger.error("❌ Error crítico: No se pudieron cargar secretos requeridos desde Secret Manager")
                    logger.error("🚨 La aplicación no puede funcionar sin acceso a la configuración de infraestructura")
                    logger.error("🔧 Verifica que todos los secretos estén configurados: python -m scripts.verify_secrets --project-id YOUR_PROJECT_ID")
                    raise RuntimeError("Secretos críticos no disponibles en Secret Manager")
            except Exception as e:
                logger.error(f"❌ Error crítico al cargar secretos desde GCP: {e}")
                logger.error("🚨 La aplicación no puede continuar sin acceso a Secret Manager")
                logger.error("🔧 Soluciones posibles:")
                logger.error("   1. Verificar que el service account tenga permisos de Secret Manager")
                logger.error("   2. Configurar secretos: python -m scripts.setup_secrets --project-id YOUR_PROJECT_ID")
                logger.error("   3. Verificar conectividad a Google Cloud APIs")
                raise RuntimeError(f"Error crítico en Secret Manager: {e}")
        elif skip_secrets:
            logger.info("⏭️  Saltando carga de secretos (SKIP_SECRETS=true)")

        # Los middlewares ya están configurados al inicio de la aplicación
        # No es necesario reconfigurarlos aquí
        logger.info("✅ Middlewares ya configurados al inicio de la aplicación")

        # 🏗️ IMPROVED: Database migrations now handled by CI/CD pipeline (BEST PRACTICE)
        # This implements the recommendation for scalable production systems
        # Skip migrations on app startup in production - they run in CI/CD pre-deployment
        skip_migrations = os.getenv("SKIP_MIGRATIONS", "true" if settings.ENV == "production" else "false").lower() == "true"
        
        if not skip_migrations:
            logger.info("🔄 Ejecutando migraciones de base de datos en startup...")
            logger.warning("⚠️  NOTA: En producción, las migraciones deberían ejecutarse en CI/CD")
            try:
                import subprocess
                
                # Change to the correct directory for alembic
                alembic_dir = "/app"
                if os.path.exists("/app/alembic"):
                    logger.info(f"📁 Ejecutando migraciones desde {alembic_dir}")
                    
                    # Run alembic upgrade head
                    result = subprocess.run(
                        ["python", "-m", "alembic", "upgrade", "head"],
                        cwd=alembic_dir,
                        capture_output=True,
                        text=True,
                        timeout=300  # 5 minute timeout
                    )
                    
                    if result.returncode == 0:
                        logger.info("✅ Migraciones de base de datos completadas exitosamente")
                    else:
                        logger.error(f"❌ Error en migraciones: {result.stderr}")
                        # En desarrollo, continuar sin fallar
                        if settings.ENV == "production":
                            raise RuntimeError(f"Database migration failed: {result.stderr}")
                        else:
                            logger.warning("⚠️  Continuando en modo desarrollo sin migraciones")
                else:
                    logger.warning("⚠️  Directorio alembic no encontrado, saltando migraciones")
                    
            except Exception as e:
                logger.error(f"❌ Error ejecutando migraciones: {e}")
                if settings.ENV == "production":
                    raise RuntimeError(f"Failed to run database migrations: {e}")
                else:
                    logger.warning("⚠️  Continuando en modo desarrollo sin migraciones")
        else:
            if settings.ENV == "production":
                logger.info("✅ PRODUCCIÓN: Migraciones manejadas por CI/CD pipeline (MEJORES PRÁCTICAS)")
                logger.info("🏗️ Las migraciones se ejecutaron en el pre-despliegue para mayor robustez")
            else:
                logger.info("⏭️  Saltando migraciones de BD (SKIP_MIGRATIONS=true)")

    yield

    # Shutdown
    logger.info("Shutting down Rayuela")
    # Cerrar pool de conexiones si es necesario (DatabaseConnectionManager podría manejarlo)
    # connection_manager = await DatabaseConnectionManager.get_instance()
    # await connection_manager.close_engine()


app = FastAPI(
    title=settings.PROJECT_NAME,
    version=settings.API_VERSION,
    docs_url="/api/docs",
    openapi_url="/api/public-openapi.json",  # SECURITY: Use public version by default
    lifespan=lifespan,
)

# Endpoint de salud básico (fuera de /api/v1) - DEBE estar ANTES del middleware
@app.get("/health", tags=["Health"])
async def health_check():
    if IS_TEMP_BACKEND:
        # SECURITY: Advertencia clara en el endpoint de salud sobre modo temporal
        return {
            "status": "healthy",
            "mode": "temporary",
            "purpose": "openapi_generation",
            "warning": "TEMPORARY MODE - NOT FOR PRODUCTION USE",
            "security_note": "Authentication and full middleware disabled"
        }
    # Podría incluir chequeos de DB y Redis aquí si es necesario
    return {"status": "healthy", "mode": "production", "security": "full"}

# Configurar middlewares y exception handlers
if not IS_TEMP_BACKEND:
    setup_middleware(app)
    register_exception_handlers(app)


# Incluir routers
app.include_router(public_router, prefix="/api/v1")  # Rutas públicas bajo /api/v1

# Incluir el webhook de Mercadopago como ruta pública
if not IS_TEMP_BACKEND:
    from src.api.v1.endpoints.billing import router as billing_router
    app.include_router(billing_router, prefix="/api/v1/billing", tags=["billing"], include_in_schema=False)

# Asegúrate que get_current_account maneje la API Key
if not IS_TEMP_BACKEND:
    app.include_router(
        private_router, prefix="/api/v1", dependencies=[Depends(get_current_account)]
    )
else:
    # In temp mode, include private router without auth for OpenAPI generation
    app.include_router(private_router, prefix="/api/v1")

# Tareas de mantenimiento (ejecutar externamente)
# if settings.ENV == "production":
#     # Usa Cloud Scheduler o similar para llamar a un endpoint protegido
#     # o ejecutar un script/worker separado.
#     pass

def find_available_port(start_port: int, max_attempts: int = 10) -> int:
    """
    Busca un puerto disponible a partir del puerto inicial.

    Args:
        start_port: Puerto inicial para la búsqueda
        max_attempts: Número máximo de intentos

    Returns:
        Puerto disponible o None si no se encuentra ninguno
    """
    import socket
    from contextlib import closing

    def is_port_available(port: int) -> bool:
        with closing(socket.socket(socket.AF_INET, socket.SOCK_STREAM)) as sock:
            try:
                sock.bind(("0.0.0.0", port))
                return True
            except socket.error:
                return False

    port = start_port
    for _ in range(max_attempts):
        if is_port_available(port):
            return port
        port += 1

    # Si no se encuentra un puerto disponible, devolver el puerto original
    # y dejar que uvicorn maneje el error
    return start_port

if __name__ == "__main__":
    import uvicorn
    import socket

    # Uvicorn se ejecuta desde Dockerfile o docker-compose
    # Usamos el host y puerto configurados en settings
    port = settings.API_PORT

    try:
        # Intentar ejecutar la aplicación en el puerto configurado
        logger.info(f"Iniciando servidor en {settings.API_HOST}:{port}")
        uvicorn.run(app, host=settings.API_HOST, port=port)
    except OSError as e:
        if "Address already in use" in str(e) or "[Errno 10048]" in str(e):
            # El puerto está ocupado, intentar encontrar uno disponible
            logger.warning(f"El puerto {port} ya está en uso. Buscando un puerto alternativo...")
            new_port = find_available_port(port + 1)
            if new_port != port:
                logger.info(f"Usando puerto alternativo: {new_port}")
                uvicorn.run(app, host=settings.API_HOST, port=new_port)
            else:
                logger.error(f"No se encontró un puerto disponible después de {port}")
                raise
        else:
            # Otro tipo de error
            logger.error(f"Error al iniciar el servidor: {e}")
            raise
