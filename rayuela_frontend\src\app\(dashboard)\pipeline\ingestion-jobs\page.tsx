"use client";

import { useState } from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';

import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Database,
  AlertCircle,
  RotateCcw,
  Eye,
  Filter,
  Search,
  Upload,
  Download,
  Loader2
} from 'lucide-react';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
// Removed unused Link import
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useIngestionJobs } from '@/lib/hooks/useIngestionJobs';
import { useJobTable, FilterStatus } from '@/lib/hooks/useJobTable';
import { DataIngestionModal } from '@/components/pipeline/DataIngestionModal';
import { formatBytes, getStatusIcon, getStatusBadge, formatDuration } from '@/lib/utils/format';
import { DensePageLayout, DenseCard, DenseTableRow } from "@/components/ui/layout";

import type { IngestionJob } from '@/lib/hooks/useIngestionJobs';

export default function IngestionJobsPage() {
  const { jobs, isLoading, error, startBatchIngestion } = useIngestionJobs();
  const [selectedJob, setSelectedJob] = useState<IngestionJob | null>(null);
  const [retryingJobId, setRetryingJobId] = useState<number | null>(null);

  const {
    filteredJobs,
    searchQuery,
    setSearchQuery,
    statusFilter,
    setStatusFilter,
    clearFilters
  } = useJobTable<IngestionJob>(jobs, (job, q) => {
    return (
      job.jobId.toString().includes(q) ||
      (job.filePath?.toLowerCase().includes(q.toLowerCase()) ?? false)
    );
  });

  // Use formatBytes for file size formatting
  const formatFileSize = (bytes: number) => formatBytes(bytes);

  const canRetryJob = (job: IngestionJob) => {
    return job.status === 'FAILED';
  };

  const handleRetryJob = async (jobId: number) => {
    const job = jobs.find(j => j.jobId === jobId);
    if (!job) return;

    setRetryingJobId(jobId);

    try {
      // For retry, we would need the original data. Since we don't store it,
      // we'll show a message asking the user to re-upload the file
      alert('Para reintentar este trabajo, por favor usa el botón "Nueva Ingesta" para subir el archivo nuevamente.');
    } catch (err) {
      console.error('Error retrying job:', err);
      alert('Error al reintentar el trabajo. Por favor intenta nuevamente.');
    } finally {
      setRetryingJobId(null);
    }
  };

  const handleDownloadFile = (filePath: string) => {
    // TODO: Implement file download logic
    console.log('Downloading file:', filePath);
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-8 space-y-8">
        <div className="bg-card/50 border border-border/50 rounded-lg p-6">
          <Skeleton className="h-8 w-64 mb-2" />
          <Skeleton className="h-4 w-96" />
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-48" />
            <Skeleton className="h-4 w-32" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-64 w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <DensePageLayout
      title="Historial de Ingesta de Datos"
      description="Seguimiento completo de todos tus procesos de carga de datos"
      actions={
        <DataIngestionModal
          onIngestionStart={startBatchIngestion}
          trigger={
            <Button>
              <Upload className="h-4 w-4 mr-2" />
              Nueva Ingesta
            </Button>
          }
        />
      }
    >
      {/* Resumen de métricas */}
      <DenseCard
        title="Resumen"
        icon={<Database className="h-6 w-6 text-green-500" />}
      >
        <div className="flex gap-4 text-sm text-muted-foreground p-6">
          <span>Total: {jobs.length}</span>
          <span>Completados: {jobs.filter(j => j.status === 'COMPLETED').length}</span>
          <span>En proceso: {jobs.filter(j => j.status === 'PROCESSING').length}</span>
          <span>Fallidos: {jobs.filter(j => j.status === 'FAILED').length}</span>
        </div>
      </DenseCard>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Filters Section */}
      <DenseCard title="Filtros" icon={<Filter className="h-5 w-5" />}>
        <CardContent className="p-6">
          <div className="flex flex-col gap-6 sm:flex-row sm:items-center sm:gap-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
                <Input
                  placeholder="Buscar por ID o archivo..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2 items-center">
              <Label htmlFor="statusFilter" className="text-sm whitespace-nowrap">Estado:</Label>
              <Select value={statusFilter} onValueChange={(value: FilterStatus) => setStatusFilter(value)}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos</SelectItem>
                  <SelectItem value="pending">Pendiente</SelectItem>
                  <SelectItem value="processing">Procesando</SelectItem>
                  <SelectItem value="completed">Completado</SelectItem>
                  <SelectItem value="failed">Fallido</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <Button variant="outline" size="sm" onClick={clearFilters}>
              Limpiar
            </Button>
          </div>
        </CardContent>
      </DenseCard>

      {/* Jobs Table */}
      <DenseCard title="Trabajos de Ingesta" description="Lista completa de procesos de carga de datos con detalles y estadísticas">
        <CardContent className="p-0">
          <div className="overflow-hidden">
            <Table>
              <TableHeader className="bg-muted/10">
                <TableRow className="border-b border-border/30">
                  <TableHead className="font-semibold">Job ID</TableHead>
                  <TableHead className="font-semibold">Estado</TableHead>
                  <TableHead className="font-semibold">Fecha Inicio</TableHead>
                  <TableHead className="font-semibold">Duración</TableHead>
                  <TableHead className="font-semibold">Registros Procesados</TableHead>
                  <TableHead className="font-semibold">Archivo</TableHead>
                  <TableHead className="text-right font-semibold">Acciones</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredJobs.length > 0 ? (
                  filteredJobs.map((job, index) => (
                    <DenseTableRow key={job.jobId} index={index}>
                      <TableCell className="font-medium py-4">#{job.jobId}</TableCell>
                      <TableCell className="py-4">
                        <div className="flex items-center gap-2">
                          {getStatusIcon(job.status)}
                          {getStatusBadge(job.status)}
                        </div>
                      </TableCell>
                      <TableCell className="py-4">
                        <div className="text-sm">
                          <div>{format(new Date(job.createdAt), 'dd/MM/yyyy', { locale: es })}</div>
                          <div className="text-muted-foreground">{format(new Date(job.createdAt), 'HH:mm', { locale: es })}</div>
                        </div>
                      </TableCell>
                      <TableCell className="py-4">
                        {job.duration ? (
                          <span className="text-sm font-medium">{formatDuration(job.duration)}</span>
                        ) : job.status === 'PROCESSING' ? (
                          <span className="text-sm text-muted-foreground">En curso</span>
                        ) : (
                          <span className="text-muted-foreground">—</span>
                        )}
                      </TableCell>
                      <TableCell className="py-4">
                        {job.recordsProcessed ? (
                          <div className="text-sm">
                            <div className="font-medium">
                              Total: {job.recordsProcessed.total?.toLocaleString() || '—'}
                            </div>
                            <div className="text-muted-foreground text-xs">
                              {job.recordsProcessed.users && `${job.recordsProcessed.users.toLocaleString()} usuarios`}
                              {job.recordsProcessed.products && `, ${job.recordsProcessed.products.toLocaleString()} productos`}
                              {job.recordsProcessed.interactions && `, ${job.recordsProcessed.interactions.toLocaleString()} interacciones`}
                            </div>
                          </div>
                        ) : (
                          <span className="text-muted-foreground">—</span>
                        )}
                      </TableCell>
                      <TableCell className="py-4">
                        {job.filePath ? (
                          <div className="text-sm">
                            <div className="font-medium truncate max-w-32" title={job.filePath}>
                              {job.filePath.split('/').pop()}
                            </div>
                            {job.fileSize && (
                              <div className="text-muted-foreground text-xs">
                                {formatFileSize(job.fileSize)}
                              </div>
                            )}
                          </div>
                        ) : (
                          <span className="text-muted-foreground">—</span>
                        )}
                      </TableCell>
                      <TableCell className="text-right py-4">
                        <div className="flex items-center justify-end gap-1">
                          <Dialog>
                            <DialogTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => setSelectedJob(job)}
                                className="h-8 w-8 p-0 hover:bg-muted/50"
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                            </DialogTrigger>
                            <DialogContent className="max-w-2xl">
                              <DialogHeader>
                                <DialogTitle>Detalles del Job #{job.jobId}</DialogTitle>
                                <DialogDescription>
                                  Información completa del trabajo de ingesta de datos
                                </DialogDescription>
                              </DialogHeader>
                              {selectedJob && (
                                <div className="space-y-4 max-h-96 overflow-y-auto">
                                  <div className="grid grid-cols-2 gap-4">
                                    <div>
                                      <Label className="text-sm font-medium">Estado</Label>
                                      <div className="mt-1">{getStatusBadge(selectedJob.status)}</div>
                                    </div>
                                    <div>
                                      <Label className="text-sm font-medium">Duración</Label>
                                      <p className="text-sm">{selectedJob.duration ? formatDuration(selectedJob.duration) : 'En curso'}</p>
                                    </div>
                                  </div>
                                  
                                  {selectedJob.filePath && (
                                    <div>
                                      <Label className="text-sm font-medium">Archivo</Label>
                                      <div className="mt-1 flex items-center gap-2">
                                        <code className="text-xs bg-muted p-2 rounded flex-1">
                                          {selectedJob.filePath}
                                        </code>
                                        <Button 
                                          size="sm" 
                                          variant="outline"
                                          onClick={() => handleDownloadFile(selectedJob.filePath!)}
                                          disabled
                                        >
                                          <Download className="h-4 w-4" />
                                        </Button>
                                      </div>
                                      {selectedJob.fileSize && (
                                        <p className="text-xs text-muted-foreground mt-1">
                                          Tamaño: {formatFileSize(selectedJob.fileSize)}
                                        </p>
                                      )}
                                    </div>
                                  )}
                                  
                                  {selectedJob.recordsProcessed && (
                                    <div>
                                      <Label className="text-sm font-medium">Registros Procesados</Label>
                                      <div className="grid grid-cols-4 gap-2 mt-2">
                                        {selectedJob.recordsProcessed.users && (
                                          <div className="bg-muted p-2 rounded">
                                            <div className="text-xs font-medium">Usuarios</div>
                                            <div className="text-sm font-bold">{selectedJob.recordsProcessed.users.toLocaleString()}</div>
                                          </div>
                                        )}
                                        {selectedJob.recordsProcessed.products && (
                                          <div className="bg-muted p-2 rounded">
                                            <div className="text-xs font-medium">Productos</div>
                                            <div className="text-sm font-bold">{selectedJob.recordsProcessed.products.toLocaleString()}</div>
                                          </div>
                                        )}
                                        {selectedJob.recordsProcessed.interactions && (
                                          <div className="bg-muted p-2 rounded">
                                            <div className="text-xs font-medium">Interacciones</div>
                                            <div className="text-sm font-bold">{selectedJob.recordsProcessed.interactions.toLocaleString()}</div>
                                          </div>
                                        )}
                                        {selectedJob.recordsProcessed.total && (
                                          <div className="bg-muted p-2 rounded">
                                            <div className="text-xs font-medium">Total</div>
                                            <div className="text-sm font-bold">{selectedJob.recordsProcessed.total.toLocaleString()}</div>
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  )}
                                  
                                  {selectedJob.errorMessage && (
                                    <div>
                                      <Label className="text-sm font-medium text-destructive">Error</Label>
                                      <Alert variant="destructive" className="mt-1">
                                        <AlertCircle className="h-4 w-4" />
                                        <AlertDescription className="text-sm">
                                          {selectedJob.errorMessage}
                                        </AlertDescription>
                                      </Alert>
                                    </div>
                                  )}
                                  
                                  {selectedJob.taskId && (
                                    <div>
                                      <Label className="text-sm font-medium">Task ID</Label>
                                      <code className="text-xs bg-muted p-1 rounded block mt-1">
                                        {selectedJob.taskId}
                                      </code>
                                    </div>
                                  )}
                                </div>
                              )}
                            </DialogContent>
                          </Dialog>
                          
                          {canRetryJob(job) && (
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-8 w-8 p-0 hover:bg-muted/50"
                                  disabled={retryingJobId === job.jobId}
                                >
                                  {retryingJobId === job.jobId ? (
                                    <Loader2 className="h-4 w-4 animate-spin" />
                                  ) : (
                                    <RotateCcw className="h-4 w-4" />
                                  )}
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Reintentar trabajo de ingesta</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    Para reintentar este trabajo fallido, necesitarás subir el archivo original nuevamente.
                                    ¿Te gustaría ir a la página de nueva ingesta?
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>Cancelar</AlertDialogCancel>
                                  <AlertDialogAction onClick={() => window.open('/pipeline', '_blank')}>
                                    Ir a Nueva Ingesta
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          )}
                        </div>
                      </TableCell>
                    </DenseTableRow>
                  ))
                ) : (
                  <DenseTableRow index={0}>
                    <TableCell colSpan={7} className="text-center py-8">
                      <div className="flex flex-col items-center gap-2 text-muted-foreground">
                        {jobs.length === 0 ? (
                          <>
                            <Database className="h-8 w-8" />
                            <p>No hay trabajos de ingesta aún</p>
                            <p className="text-sm">Los trabajos aparecerán aquí cuando subas datos</p>
                          </>
                        ) : (
                          <>
                            <Search className="h-8 w-8" />
                            <p>No se encontraron trabajos con los filtros aplicados</p>
                            <Button variant="outline" size="sm" onClick={clearFilters}>
                              Limpiar filtros
                            </Button>
                          </>
                        )}
                      </div>
                    </TableCell>
                  </DenseTableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </DenseCard>

      {/* Information */}
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Información sobre ingesta de datos</AlertTitle>
        <AlertDescription>
          <div className="space-y-2 text-sm mt-2">
            <p>
              Los trabajos de ingesta procesan archivos de datos (CSV, JSON) para actualizar 
              usuarios, productos e interacciones en el sistema.
            </p>
            <ul className="list-disc list-inside space-y-1 pl-2">
              <li><strong>Formatos soportados:</strong> CSV, JSON</li>
              <li><strong>Tipos de datos:</strong> Usuarios, Productos, Interacciones</li>
              <li><strong>Validación:</strong> Se valida formato y campos obligatorios</li>
              <li><strong>Procesamiento:</strong> Los datos se procesan de forma asíncrona</li>
            </ul>
          </div>
        </AlertDescription>
      </Alert>
    </DensePageLayout>
  );
} 