// src/components/dashboard/DashboardWidgets.tsx
"use client";

import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { BarChart3Icon, KeyIcon, CreditCardIcon, Upload, Play, Zap } from 'lucide-react';
import Link from 'next/link';
import { formatBytes, formatNumber, formatDate } from '@/lib/utils/format';
import type { UsageSummaryResponse, AccountResponse } from '@/lib/generated/rayuelaAPI';
import { DataIngestionModal } from '@/components/pipeline/DataIngestionModal';
import { TrainingModal } from '@/components/pipeline/TrainingModal';
import { useIngestionJobs } from '@/lib/hooks/useIngestionJobs';
import { useTrainingJobs } from '@/lib/hooks/useTrainingJobs';

interface DashboardWidgetsProps {
  usageData: UsageSummaryResponse | null;
  usageError: string | null;
  accountData: AccountResponse | null;
  accountError: string | null;
  apiKey: string | null;
  isDataLoading: boolean;
}

// Quick Actions Widget Component
const QuickActionsWidget = () => {
  const { startBatchIngestion } = useIngestionJobs();
  const { startTraining } = useTrainingJobs();

  return (
    <Card className="border-2 border-primary/20 shadow-soft hover:shadow-medium transition-all duration-300">
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center text-heading-md">
          <Zap className="h-5 w-5 mr-2 text-primary" />
          Acciones Rápidas
        </CardTitle>
        <CardDescription>
          Inicia operaciones comunes de datos y modelos
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-3">
        <DataIngestionModal
          onIngestionStart={startBatchIngestion}
          trigger={
            <Button size="sm" className="w-full justify-start">
              <Upload className="h-4 w-4 mr-2" />
              Nueva Ingesta de Datos
            </Button>
          }
        />

        <TrainingModal
          onTrainingStart={startTraining}
          trigger={
            <Button variant="outline" size="sm" className="w-full justify-start">
              <Play className="h-4 w-4 mr-2" />
              Iniciar Entrenamiento
            </Button>
          }
        />
      </CardContent>
      <CardFooter>
        <Button asChild variant="ghost" size="sm" className="w-full">
          <Link href="/pipeline" className="flex items-center justify-center">
            <BarChart3Icon className="mr-2 h-4 w-4" />
            Ver Pipeline Completo
          </Link>
        </Button>
      </CardFooter>
    </Card>
  );
};

// Helper component for usage data display
const UsageDataDisplay = ({ usageData }: { usageData: UsageSummaryResponse }) => (
  <div className="grid grid-cols-2 gap-4">
    <div className="p-3 bg-info-light rounded-lg">
      <div className="text-sm text-info font-medium">Llamadas API</div>
      <div className="text-metric mt-1 text-info">
        {formatNumber(usageData.apiCalls?.used || 0)}
      </div>
      <div className="text-xs text-info/70 mt-1">
        Uso actual
      </div>
    </div>
    <div className="p-3 bg-success-light rounded-lg">
      <div className="text-sm text-success font-medium">Almacenamiento</div>
      <div className="text-metric mt-1 text-success">
        {formatBytes(usageData.storage?.usedBytes || 0)}
      </div>
      <div className="text-xs text-success/70 mt-1">
        Uso actual
      </div>
    </div>
  </div>
);

// Helper component for API key display
const ApiKeyDisplay = ({ apiKey }: { apiKey: string }) => (
  <div className="space-y-2">
    <div className="text-sm font-mono bg-muted p-2 rounded border">
      {apiKey.slice(0, 4)}...{apiKey.slice(-4)}
    </div>
    <div className="text-xs text-muted-foreground">
      API Key activa
    </div>
  </div>
);

// Helper component for subscription display
const SubscriptionDisplay = ({ accountData }: { accountData: AccountResponse }) => {
  if (accountData?.subscription) {
    return (
      <div className="space-y-2">
        <div className="flex items-center">
          <div className={`w-2 h-2 rounded-full mr-2 ${
            accountData.subscription.isActive ? 'bg-success' : 'bg-destructive'
          }`}></div>
          <span className="text-sm font-medium">
            {accountData.subscription.plan || 'Free'}
          </span>
        </div>
        <div className="text-xs text-muted-foreground">
          {accountData.subscription.isActive ? 'Activa' : 'Inactiva'}
          {accountData.subscription.expiresAt && (
            <span> • Expira {formatDate(accountData.subscription.expiresAt)}</span>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <div className="flex items-center">
        <div className="w-2 h-2 bg-info rounded-full mr-2"></div>
        <span className="text-sm font-medium">Plan Free</span>
      </div>
      <div className="text-xs text-muted-foreground">
        Plan básico activo
      </div>
    </div>
  );
};

export default function DashboardWidgets({
  usageData,
  usageError,
  accountData,
  accountError,
  apiKey,
  isDataLoading
}: DashboardWidgetsProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6 mb-6">
      {/* Widget de Uso Rápido */}
      <Card className="col-span-1 md:col-span-2 lg:col-span-2 border-2 border-info/20 shadow-soft hover:shadow-medium transition-all duration-300">
        <CardHeader className="pb-2">
          <CardTitle className="flex items-center text-heading-md">
            <BarChart3Icon className="h-5 w-5 mr-2 text-info" />
            Resumen de Uso
          </CardTitle>
          <CardDescription>
            Vista rápida de tu consumo actual
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isDataLoading ? (
            <div className="space-y-2">
              <Skeleton className="h-6 w-full" />
              <Skeleton className="h-4 w-3/4" />
            </div>
          ) : usageError ? (
            <p className="text-destructive text-sm">Error al cargar datos de uso</p>
          ) : usageData && accountData ? (
            <UsageDataDisplay usageData={usageData} />
          ) : (
            <p className="text-muted-foreground">No hay datos de uso disponibles</p>
          )}
        </CardContent>
        <CardFooter>
          <Button asChild variant="outline" size="sm" className="w-full">
            <Link href="/usage" className="flex items-center justify-center">
              <BarChart3Icon className="mr-2 h-4 w-4" />
              Ver Panel de Uso Completo
            </Link>
          </Button>
        </CardFooter>
      </Card>

      {/* Widget de API Key Actual */}
      <Card className="border-2 border-warning/20 shadow-soft hover:shadow-medium transition-all duration-300">
        <CardHeader className="pb-2">
          <CardTitle className="flex items-center text-heading-md">
            <KeyIcon className="h-5 w-5 mr-2 text-warning" />
            API Key Actual
          </CardTitle>
          <CardDescription>
            Información de tu clave activa
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isDataLoading ? (
            <div className="space-y-2">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-3 w-2/3" />
            </div>
          ) : accountError ? (
            <p className="text-destructive text-sm">Error al cargar datos de la cuenta</p>
          ) : apiKey ? (
            <ApiKeyDisplay apiKey={apiKey} />
          ) : (
            <p className="text-muted-foreground text-sm">No hay API Key disponible</p>
          )}
        </CardContent>
      </Card>

      {/* Widget de Estado de Cuenta */}
      <Card className="border-2 border-success/20 shadow-soft hover:shadow-medium transition-all duration-300">
        <CardHeader className="pb-2">
          <CardTitle className="flex items-center text-heading-md">
            <CreditCardIcon className="h-5 w-5 mr-2 text-success" />
            Estado de Cuenta
          </CardTitle>
          <CardDescription>
            Estado actual de tu suscripción
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isDataLoading ? (
            <div className="space-y-2">
              <Skeleton className="h-6 w-full" />
              <Skeleton className="h-4 w-3/4" />
            </div>
          ) : accountError ? (
            <p className="text-destructive text-sm">Error al cargar datos de suscripción</p>
          ) : (
            <SubscriptionDisplay accountData={accountData} />
          )}
        </CardContent>
      </Card>
    </div>
  );
}
