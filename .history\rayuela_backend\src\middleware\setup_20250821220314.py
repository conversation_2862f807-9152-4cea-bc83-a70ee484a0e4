# src/middleware/setup.py
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware

# Quitar HTTPSRedirectMiddleware si tu proxy/LB maneja la redirección
# from fastapi.middleware.httpsredirect import HTTPSRedirectMiddleware
from starlette.middleware.sessions import SessionMiddleware  # Si usas sesiones

from src.core.config import settings, get_allowed_origins
from src.utils.base_logger import logger
from src.middleware.tenant import TenantMiddleware

# Importar los middlewares específicos
from .audit import AuditMiddleware
from .error_handling import ErrorHandlingMiddleware
from .timing_logger import TimingMiddleware
from .cache_invalidation_middleware import CacheInvalidationMiddleware
from .usage_meter_middleware import UsageMeterMiddleware
from src.middleware.request_id_middleware import RequestIDMiddleware


def setup_middleware(app: FastAPI):
    """Configura y registra todos los middlewares de la aplicación."""

    # 1. RequestID Middleware (debe ser el primero para capturar todas las requests)
    app.add_middleware(RequestIDMiddleware)

    # 2. Error Handling (debe ir temprano para capturar errores de otros middlewares)
    app.add_middleware(ErrorHandlingMiddleware)

    # 3. CORS - Controla qué orígenes pueden hacer solicitudes a la API
    # SECURITY: Esta configuración es crítica para prevenir ataques CSRF
    # - NO usar allow_origins=["*"] en producción
    # - Lista explícita de orígenes permitidos obtenida de get_allowed_origins()
    # - Regex específico solo para dominios Cloud Run de Rayuela
    allowed_origins = get_allowed_origins()

    # Log de configuración CORS para auditoría de seguridad
    from src.utils.base_logger import log_info
    log_info("CORS Configuration", {
        "allowed_origins": allowed_origins,
        "allow_credentials": True,
        "environment": os.getenv("ENV", "development")
    })

    # Verificación de seguridad: asegurar que no hay wildcards en producción
    if os.getenv("ENV") == "production" and "*" in allowed_origins:
        raise ValueError("SECURITY ERROR: Wildcard origins not allowed in production")

    app.add_middleware(
        CORSMiddleware,
        allow_origins=allowed_origins,
        # Aceptar dinámicamente cualquier dominio de Cloud Run para el frontend
        # con patrón rayuela-frontend-*.run.app (incluye hash, ID y número)
        allow_origin_regex=r"https://rayuela-frontend-[a-zA-Z0-9\-\.]+\.run\.app",
        allow_credentials=True,  # Necesario para JWT y API keys
        allow_methods=[
            "GET",
            "POST",
            "PUT",
            "PATCH",
            "DELETE",
            "OPTIONS",
            "HEAD",
        ],
        allow_headers=[
            "accept",
            "accept-encoding",
            "authorization",
            "content-type",
            "dnt",
            "origin",
            "user-agent",
            "x-csrftoken",
            "x-requested-with",
            "x-api-key",
            "x-tenant-id",
            "cache-control",
        ],
    )

    # 4. Trusted Hosts - Protege contra ataques de Host header spoofing
    # Configuramos con todos los hosts posibles para evitar problemas de timing con Secret Manager
    default_hosts = ["localhost", "127.0.0.1", "0.0.0.0"]
    production_hosts = [
        "rayuela-backend-1002953244539.us-central1.run.app",
        "rayuela-backend-lrw7xazcbq-uc.a.run.app",
        "rayuela-backend-517524826322.us-central1.run.app"
    ]
    all_allowed_hosts = default_hosts + production_hosts

    logger.info(f"🔒 Configurando TrustedHostMiddleware con hosts: {all_allowed_hosts}")
    app.add_middleware(TrustedHostMiddleware, allowed_hosts=all_allowed_hosts)

    # 5. GZip Compression
    app.add_middleware(GZipMiddleware, minimum_size=1000)

    # 6. Timing Logger
    app.add_middleware(TimingMiddleware)

    # 7. Audit Middleware
    app.add_middleware(AuditMiddleware)

    # 8. Cache Invalidation Middleware (para invalidar caché en interacciones)
    app.add_middleware(CacheInvalidationMiddleware)

    # 9. Usage Meter Middleware (para contar y limitar llamadas a la API)
    # Este middleware maneja tanto el conteo de API calls como el rate limiting
    app.add_middleware(UsageMeterMiddleware)

    # 10. Session Middleware (si es necesario, requiere SECRET_KEY)
    # if settings.SECRET_KEY:
    #     app.add_middleware(SessionMiddleware, secret_key=settings.SECRET_KEY)

    # 11. Security Headers (como middleware de función)
    @app.middleware("http")
    async def add_security_headers(request, call_next):
        response = await call_next(request)
        # Headers básicos de seguridad
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        # response.headers["X-XSS-Protection"] = "1; mode=block" # Obsoleto en navegadores modernos
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        # CSP es complejo, configúralo cuidadosamente si lo necesitas
        # response.headers["Content-Security-Policy"] = "default-src 'self'; ..."
        # HSTS solo si estás seguro de que SIEMPRE servirás sobre HTTPS
        # if settings.ENV == "production":
        #    response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
        return response

    # Middleware de tenant para RLS
    app.add_middleware(TenantMiddleware)

    logger.info("Application middleware configured.")
