"""Data model improvements: JSON to JSONB, Search results_metadata, MercadoPago field optimization

Revision ID: 20250822_data_model_improvements
Revises: [previous_revision]
Create Date: 2025-08-22 01:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '20250822_data_model_improvements'
down_revision = None  # Replace with actual previous revision
branch_labels = None
depends_on = None


def upgrade():
    """Apply data model improvements."""
    
    # 1. Convert JSON columns to JSONB for better performance
    print("Converting JSON columns to JSONB...")
    
    # Experiment model
    op.execute("ALTER TABLE experiments ALTER COLUMN control_config TYPE JSONB USING control_config::jsonb")
    op.execute("ALTER TABLE experiments ALTER COLUMN treatment_config TYPE JSONB USING treatment_config::jsonb")
    op.execute("ALTER TABLE experiments ALTER COLUMN tags TYPE JSONB USING tags::jsonb")
    
    # ExperimentResult model
    op.execute("ALTER TABLE experiment_results ALTER COLUMN additional_metrics TYPE JSONB USING additional_metrics::jsonb")
    
    # ExperimentEvent model
    op.execute("ALTER TABLE experiment_events ALTER COLUMN additional_context TYPE JSONB USING additional_context::jsonb")
    
    # ModelMetric model
    op.execute("ALTER TABLE model_metrics ALTER COLUMN additional_metrics TYPE JSONB USING additional_metrics::jsonb")
    
    # TrainingJob model
    op.execute("ALTER TABLE training_jobs ALTER COLUMN parameters TYPE JSONB USING parameters::jsonb")
    op.execute("ALTER TABLE training_jobs ALTER COLUMN metrics TYPE JSONB USING metrics::jsonb")
    
    # TrainingMetrics model
    op.execute("ALTER TABLE training_metrics ALTER COLUMN additional_metrics TYPE JSONB USING additional_metrics::jsonb")
    
    # 2. Add results_metadata column to Search model
    print("Adding results_metadata column to searches table...")
    op.add_column('searches', sa.Column('results_metadata', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='Metadatos sobre los resultados de la búsqueda (ej. IDs de productos, total de resultados)'))
    
    # 3. Optimize MercadoPago field lengths
    print("Optimizing MercadoPago field lengths...")
    
    # Account model
    op.alter_column('accounts', 'mercadopago_customer_id',
                   existing_type=sa.VARCHAR(length=255),
                   type_=sa.String(length=20),
                   existing_nullable=True,
                   existing_comment='Mercado Pago Customer ID')
    
    # Subscription model
    op.alter_column('subscriptions', 'mercadopago_subscription_id',
                   existing_type=sa.VARCHAR(length=255),
                   type_=sa.String(length=20),
                   existing_nullable=True)
    
    op.alter_column('subscriptions', 'mercadopago_price_id',
                   existing_type=sa.VARCHAR(length=255),
                   type_=sa.String(length=20),
                   existing_nullable=True)
    
    # 4. Add comments to deprecated fields in Subscription
    print("Adding deprecation comments to subscription usage fields...")
    op.alter_column('subscriptions', 'monthly_api_calls_used',
                   existing_type=sa.INTEGER(),
                   existing_nullable=False,
                   existing_server_default=sa.text('0'),
                   comment='DEPRECATED: Use AccountUsageMetrics.billing_period_api_calls')
    
    op.alter_column('subscriptions', 'storage_used',
                   existing_type=sa.BIGINT(),
                   existing_nullable=False,
                   existing_server_default=sa.text('0'),
                   comment='DEPRECATED: Use AccountUsageMetrics.billing_period_storage')
    
    op.alter_column('subscriptions', 'last_reset_date',
                   existing_type=postgresql.TIMESTAMP(timezone=True),
                   existing_nullable=True,
                   comment='DEPRECATED: Use AccountUsageMetrics.last_billing_cycle')
    
    print("Data model improvements completed successfully!")


def downgrade():
    """Revert data model improvements."""
    
    # 4. Remove deprecation comments from Subscription fields
    print("Removing deprecation comments from subscription usage fields...")
    op.alter_column('subscriptions', 'monthly_api_calls_used',
                   existing_type=sa.INTEGER(),
                   existing_nullable=False,
                   existing_server_default=sa.text('0'),
                   comment=None)
    
    op.alter_column('subscriptions', 'storage_used',
                   existing_type=sa.BIGINT(),
                   existing_nullable=False,
                   existing_server_default=sa.text('0'),
                   comment=None)
    
    op.alter_column('subscriptions', 'last_reset_date',
                   existing_type=postgresql.TIMESTAMP(timezone=True),
                   existing_nullable=True,
                   comment=None)
    
    # 3. Revert MercadoPago field lengths
    print("Reverting MercadoPago field lengths...")
    
    # Subscription model
    op.alter_column('subscriptions', 'mercadopago_price_id',
                   existing_type=sa.String(length=20),
                   type_=sa.VARCHAR(length=255),
                   existing_nullable=True)
    
    op.alter_column('subscriptions', 'mercadopago_subscription_id',
                   existing_type=sa.String(length=20),
                   type_=sa.VARCHAR(length=255),
                   existing_nullable=True)
    
    # Account model
    op.alter_column('accounts', 'mercadopago_customer_id',
                   existing_type=sa.String(length=20),
                   type_=sa.VARCHAR(length=255),
                   existing_nullable=True,
                   existing_comment='Mercado Pago Customer ID')
    
    # 2. Remove results_metadata column from Search model
    print("Removing results_metadata column from searches table...")
    op.drop_column('searches', 'results_metadata')
    
    # 1. Revert JSONB columns back to JSON
    print("Reverting JSONB columns back to JSON...")
    
    # TrainingMetrics model
    op.execute("ALTER TABLE training_metrics ALTER COLUMN additional_metrics TYPE JSON USING additional_metrics::json")
    
    # TrainingJob model
    op.execute("ALTER TABLE training_jobs ALTER COLUMN metrics TYPE JSON USING metrics::json")
    op.execute("ALTER TABLE training_jobs ALTER COLUMN parameters TYPE JSON USING parameters::json")
    
    # ModelMetric model
    op.execute("ALTER TABLE model_metrics ALTER COLUMN additional_metrics TYPE JSON USING additional_metrics::json")
    
    # ExperimentEvent model
    op.execute("ALTER TABLE experiment_events ALTER COLUMN additional_context TYPE JSON USING additional_context::json")
    
    # ExperimentResult model
    op.execute("ALTER TABLE experiment_results ALTER COLUMN additional_metrics TYPE JSON USING additional_metrics::json")
    
    # Experiment model
    op.execute("ALTER TABLE experiments ALTER COLUMN tags TYPE JSON USING tags::json")
    op.execute("ALTER TABLE experiments ALTER COLUMN treatment_config TYPE JSON USING treatment_config::json")
    op.execute("ALTER TABLE experiments ALTER COLUMN control_config TYPE JSON USING control_config::json")
    
    print("Data model improvements reverted successfully!")
