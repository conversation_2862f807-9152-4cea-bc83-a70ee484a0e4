# No necesitamos importar AsyncSession ni AuditLogCreate directamente
# ya que los usamos a través de write_audit_log_to_db
from src.utils.audit import write_audit_log_to_db
from src.utils.base_logger import log_info, log_error
from src.db.session import get_db


async def write_audit_log_to_db_task(audit_data: dict):
    """
    Tarea en segundo plano para escribir un registro de auditoría en la base de datos.
    Esta función se ejecuta como una tarea en segundo plano y no debe interrumpir
    el flujo principal de la aplicación.

    Args:
        audit_data: Datos del registro de auditoría
    """
    # Verificar que account_id esté presente
    if "account_id" not in audit_data or audit_data.get("account_id") is None:
        log_error("No se puede crear registro de auditoría sin account_id")
        return

    try:
        # Obtener una sesión de base de datos
        db = None
        try:
            db_generator = get_db()
            db = await anext(db_generator)

            # Iniciar transacción
            async with db.begin():
                # Convertir los datos a un esquema AuditLogCreate
                # Mapear los campos del middleware a los campos del esquema
                audit_log_data = {
                    "action": audit_data.get("method", "API_REQUEST"),
                    "entity_type": "API_REQUEST",
                    "entity_id": audit_data.get("url", ""),
                    "changes": {
                        "status_code": audit_data.get("status_code"),
                        "process_time": audit_data.get("process_time"),
                        "client_ip": audit_data.get("client_ip"),
                        "user_agent": audit_data.get("user_agent"),
                    },
                    "performed_by": audit_data.get("user_id", "anonymous"),
                    "details": None,  # Detalles adicionales si es necesario
                }

                # Escribir en la base de datos
                await write_audit_log_to_db(audit_log_data, db)

                # El commit se realiza automáticamente al salir del bloque begin()
                log_info(
                    f"Registro de auditoría creado para account_id={audit_data.get('account_id')}"
                )

        finally:
            # Cerrar la sesión si se abrió
            if db is not None:
                await db.close()

    except Exception as e:
        log_error(f"Error en tarea de fondo para registro de auditoría: {str(e)}")
        # No relanzar la excepción para no interrumpir el flujo principal
