"use client";

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  <PERSON>alogTitle,
  <PERSON><PERSON>Trigger,
  DialogFooter
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Upload, FileText, AlertCircle, CheckCircle, Loader2, ArrowRight, MapPin } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import Papa from 'papaparse';
import type { BatchIngestionRequest } from '@/lib/generated/rayuelaAPI';

interface DataIngestionModalProps {
  onIngestionStart: (data: BatchIngestionRequest) => Promise<unknown>;
  trigger?: React.ReactNode;
}

type DataType = 'users' | 'products' | 'interactions' | 'batch';

// Field mapping types
interface FieldMapping {
  [csvColumn: string]: string; // Maps CSV column to Rayuela field
}

interface CSVPreview {
  headers: string[];
  rows: string[][];
}

export function DataIngestionModal({ onIngestionStart, trigger }: DataIngestionModalProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [dataType, setDataType] = useState<DataType>('batch');
  const [file, setFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [jobResult, setJobResult] = useState<any>(null);
  const [csvPreview, setCsvPreview] = useState<CSVPreview | null>(null);
  const [fieldMapping, setFieldMapping] = useState<FieldMapping>({});
  const [showMapping, setShowMapping] = useState(false);

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile) {
      // Validate file type
      const validTypes = ['text/csv', 'application/json', 'text/plain'];
      const validExtensions = ['.csv', '.json', '.txt'];
      const fileExtension = selectedFile.name.toLowerCase().substring(selectedFile.name.lastIndexOf('.'));

      if (!validTypes.includes(selectedFile.type) && !validExtensions.includes(fileExtension)) {
        setError('Por favor selecciona un archivo CSV o JSON válido');
        return;
      }

      // Validate file size (10MB max)
      const maxSize = 10 * 1024 * 1024; // 10MB
      if (selectedFile.size > maxSize) {
        setError('El archivo es demasiado grande. Máximo 10MB permitido');
        return;
      }

      setFile(selectedFile);
      setError(null);

      // If it's a CSV file, parse it for preview and field mapping
      if (fileExtension === '.csv') {
        try {
          const text = await readFileContent(selectedFile);
          Papa.parse(text, {
            header: false,
            preview: 6, // Only parse first 6 rows for preview
            skipEmptyLines: true,
            complete: (results) => {
              if (results.data && results.data.length > 0) {
                const headers = results.data[0] as string[];
                const rows = results.data.slice(1) as string[][];
                setCsvPreview({ headers, rows });
                setShowMapping(true);

                // Initialize field mapping with smart defaults
                const initialMapping: FieldMapping = {};
                headers.forEach(header => {
                  const lowerHeader = header.toLowerCase();
                  if (lowerHeader.includes('external') && lowerHeader.includes('id')) {
                    initialMapping[header] = 'externalId';
                  } else if (lowerHeader.includes('name')) {
                    initialMapping[header] = 'name';
                  } else if (lowerHeader.includes('email')) {
                    initialMapping[header] = 'email';
                  } else if (lowerHeader.includes('category')) {
                    initialMapping[header] = 'category';
                  } else if (lowerHeader.includes('price')) {
                    initialMapping[header] = 'price';
                  } else if (lowerHeader.includes('user') && lowerHeader.includes('id')) {
                    initialMapping[header] = 'userId';
                  } else if (lowerHeader.includes('product') && lowerHeader.includes('id')) {
                    initialMapping[header] = 'productId';
                  }
                });
                setFieldMapping(initialMapping);
              }
            },
            error: (error) => {
              setError(`Error al parsear CSV: ${error.message}`);
            }
          });
        } catch (err) {
          setError('Error al leer el archivo CSV');
        }
      } else {
        setCsvPreview(null);
        setShowMapping(false);
        setFieldMapping({});
      }
    }
  };

  const handleSubmit = async () => {
    if (!file) {
      setError('Por favor selecciona un archivo');
      return;
    }

    // For CSV files, validate field mapping
    if (file.name.toLowerCase().endsWith('.csv') && showMapping) {
      const mappedFields = Object.values(fieldMapping).filter(Boolean);
      if (mappedFields.length === 0) {
        setError('Por favor mapea al menos un campo del CSV');
        return;
      }
    }

    setIsUploading(true);
    setError(null);

    try {
      let parsedData: Partial<BatchIngestionRequest>;

      if (file.name.toLowerCase().endsWith('.json')) {
        // Parse JSON file
        const fileContent = await readFileContent(file);
        parsedData = JSON.parse(fileContent);
      } else if (file.name.toLowerCase().endsWith('.csv')) {
        // Parse CSV file with field mapping
        parsedData = await parseCSVWithMapping(file, fieldMapping, dataType);
      } else {
        // Fallback for other text files
        const fileContent = await readFileContent(file);
        parsedData = JSON.parse(fileContent);
      }

      // Structure the data according to the BatchIngestionRequest schema
      // Only include users, products, and interactions fields
      const batchData = {
        ...parsedData
      };

      // Log the data being sent for debugging
      console.log('Sending batch data:', JSON.stringify(batchData, null, 2));

      const result = await onIngestionStart(batchData);
      console.log('Ingestion started:', result);
      setJobResult(result);
      setSuccess(true);

      // Reset form after 5 seconds to give user time to see job info
      setTimeout(() => {
        setIsOpen(false);
        setSuccess(false);
        setJobResult(null);
        setFile(null);
        setDataType('batch');
        setCsvPreview(null);
        setFieldMapping({});
        setShowMapping(false);
      }, 5000);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error iniciando ingesta de datos');
    } finally {
      setIsUploading(false);
    }
  };

  // Helper function to read file content
  const readFileContent = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => resolve(e.target?.result as string);
      reader.onerror = () => reject(new Error('Error reading file'));
      reader.readAsText(file);
    });
  };

  // Helper function to convert snake_case to camelCase (currently unused but kept for future use)
  // const toCamelCase = (str: string): string => {
  //   return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
  // };

  // Helper function to parse CSV with field mapping using papaparse
  const parseCSVWithMapping = async (file: File, mapping: FieldMapping, type: DataType): Promise<Partial<BatchIngestionRequest>> => {
    const fileContent = await readFileContent(file);

    return new Promise((resolve, reject) => {
      Papa.parse(fileContent, {
        header: true,
        skipEmptyLines: true,
        transformHeader: (header) => {
          // Use the mapped field name if available, otherwise keep original
          return mapping[header] || header;
        },
        transform: (value, field) => {
          // Convert numeric values based on field type
          if (['price', 'averageRating', 'value', 'priceRangeMin', 'priceRangeMax'].includes(field)) {
            const num = parseFloat(value);
            return !isNaN(num) ? num : value;
          } else if (['numRatings', 'inventoryCount', 'userId', 'productId'].includes(field)) {
            const num = parseInt(value, 10);
            return !isNaN(num) ? num : value;
          }
          return value;
        },
        complete: (results) => {
          if (results.errors.length > 0) {
            reject(new Error(`Error al parsear CSV: ${results.errors[0].message}`));
            return;
          }

          const data = results.data as Record<string, unknown>[];

          // Structure according to data type
          switch (type) {
            case 'users':
              resolve({ users: data });
              break;
            case 'products':
              resolve({ products: data });
              break;
            case 'interactions':
              resolve({ interactions: data });
              break;
            case 'batch':
              // For batch, try to separate based on mapped fields
              const users = data.filter(item =>
                item.externalId && !item.productId && !item.userId &&
                (item.email || item.preferredCategories)
              );
              const products = data.filter(item =>
                item.externalId && item.name && !item.userId
              );
              const interactions = data.filter(item =>
                (item.userId || item.userExternalId) && (item.productId || item.productExternalId)
              );
              resolve({ users, products, interactions });
              break;
            default:
              resolve({});
          }
        },
        error: (error) => {
          reject(new Error(`Error al parsear CSV: ${error.message}`));
        }
      });
    });
  };

  const handleClose = () => {
    if (!isUploading) {
      setIsOpen(false);
      setFile(null);
      setError(null);
      setSuccess(false);
      setDataType('batch');
      setCsvPreview(null);
      setFieldMapping({});
      setShowMapping(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button>
            <Upload className="h-4 w-4 mr-2" />
            Nueva Ingesta
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Nueva Ingesta de Datos</DialogTitle>
          <DialogDescription>
            Sube un archivo CSV o JSON con tus datos de usuarios, productos o interacciones
          </DialogDescription>
        </DialogHeader>

        {success ? (
          <div className="flex flex-col items-center py-6">
            <CheckCircle className="h-12 w-12 text-green-500 mb-4" />
            <p className="text-lg font-semibold text-green-700">¡Ingesta iniciada!</p>
            <p className="text-sm text-muted-foreground">Tu archivo está siendo procesado</p>
          </div>
        ) : (
          <div className="space-y-4">
            <div>
              <Label htmlFor="dataType">Tipo de datos</Label>
              <Select value={dataType} onValueChange={(value: DataType) => setDataType(value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecciona el tipo de datos" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="batch">Lote completo (usuarios, productos, interacciones)</SelectItem>
                  <SelectItem value="users">Solo usuarios</SelectItem>
                  <SelectItem value="products">Solo productos</SelectItem>
                  <SelectItem value="interactions">Solo interacciones</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="file">Archivo</Label>
              <Input
                id="file"
                type="file"
                accept=".csv,.json,.txt"
                onChange={handleFileChange}
                disabled={isUploading}
              />
              {file && (
                <div className="mt-2 flex items-center text-sm text-muted-foreground">
                  <FileText className="h-4 w-4 mr-2" />
                  <span>{file.name} ({(file.size / 1024).toFixed(1)} KB)</span>
                </div>
              )}
            </div>

            {/* Field Mapping UI for CSV files */}
            {showMapping && csvPreview && (
              <div className="space-y-4 border rounded-lg p-4 bg-muted/20">
                <div className="flex items-center gap-2">
                  <ArrowRight className="h-4 w-4 text-primary" />
                  <h4 className="font-medium">Mapeo de Campos</h4>
                  <Badge variant="secondary" className="text-xs">
                    {csvPreview.headers.length} columnas detectadas
                  </Badge>
                </div>

                <div className="text-sm text-muted-foreground">
                  Mapea las columnas de tu CSV a los campos de Rayuela:
                </div>

                <div className="grid gap-3 max-h-48 overflow-y-auto">
                  {csvPreview.headers.map((header, index) => (
                    <div key={header} className="flex items-center gap-3">
                      <div className="flex-1 min-w-0">
                        <div className="font-medium text-sm truncate">{header}</div>
                        <div className="text-xs text-muted-foreground truncate">
                          Ej: {csvPreview.rows[0]?.[index] || 'N/A'}
                        </div>
                      </div>
                      <ArrowRight className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                      <div className="flex-1">
                        <Select
                          value={fieldMapping[header] || ''}
                          onValueChange={(value) =>
                            setFieldMapping(prev => ({ ...prev, [header]: value }))
                          }
                        >
                          <SelectTrigger className="h-8 text-xs">
                            <SelectValue placeholder="Seleccionar campo" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="">No mapear</SelectItem>
                            <SelectItem value="externalId">ID Externo</SelectItem>
                            <SelectItem value="name">Nombre</SelectItem>
                            <SelectItem value="email">Email</SelectItem>
                            <SelectItem value="category">Categoría</SelectItem>
                            <SelectItem value="price">Precio</SelectItem>
                            <SelectItem value="description">Descripción</SelectItem>
                            <SelectItem value="userId">ID Usuario</SelectItem>
                            <SelectItem value="productId">ID Producto</SelectItem>
                            <SelectItem value="userExternalId">ID Externo Usuario</SelectItem>
                            <SelectItem value="productExternalId">ID Externo Producto</SelectItem>
                            <SelectItem value="interactionType">Tipo Interacción</SelectItem>
                            <SelectItem value="value">Valor</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  ))}
                </div>

                <div className="text-xs text-muted-foreground">
                  Vista previa: {csvPreview.rows.length} filas de datos detectadas
                </div>
              </div>
            )}

            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <div className="text-xs text-muted-foreground">
              <p>Formatos soportados: CSV, JSON</p>
              <p>Tamaño máximo: 10MB</p>
            </div>
          </div>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={isUploading}>
            Cancelar
          </Button>
          {!success && (
            <Button onClick={handleSubmit} disabled={!file || isUploading}>
              {isUploading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Subiendo...
                </>
              ) : (
                <>
                  <Upload className="h-4 w-4 mr-2" />
                  Iniciar Ingesta
                </>
              )}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 