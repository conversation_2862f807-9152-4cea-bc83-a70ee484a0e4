#!/usr/bin/env python3
"""
Script para verificar qué tablas existen en la base de datos actual.
"""

import asyncio
import sys
from pathlib import Path

# Agregar src al path
backend_root = Path(__file__).parent.parent
sys.path.insert(0, str(backend_root))
sys.path.insert(0, str(backend_root / "src"))

from src.db.session import get_async_session
from src.utils.base_logger import log_info
from sqlalchemy import text


async def check_existing_tables():
    """Verifica qué tablas existen en la base de datos."""
    
    db = await get_async_session()
    try:
        # Obtener lista de todas las tablas
        tables_query = text("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            ORDER BY table_name
        """)
        
        result = await db.execute(tables_query)
        tables = [row[0] for row in result.fetchall()]
        
        print("📊 TABLAS EXISTENTES EN LA BASE DE DATOS:")
        print("=" * 50)
        
        if not tables:
            print("❌ No se encontraron tablas en el esquema 'public'")
            return
        
        for table in tables:
            print(f"  ✅ {table}")
        
        print(f"\n📈 Total de tablas: {len(tables)}")
        
        # Verificar tablas específicas mencionadas en el informe
        expected_tables = [
            'experiments', 'experiment_results', 'experiment_events',
            'model_metrics', 'training_jobs', 'training_metrics',
            'searches', 'accounts', 'subscriptions'
        ]
        
        print("\n🔍 VERIFICACIÓN DE TABLAS DEL INFORME:")
        print("=" * 50)
        
        for table in expected_tables:
            exists = table in tables
            status = "✅ Existe" if exists else "❌ No existe"
            print(f"  {table}: {status}")
        
        # Si existen accounts y subscriptions, verificar sus columnas
        if 'accounts' in tables:
            print("\n💳 COLUMNAS DE ACCOUNTS:")
            columns_query = text("""
                SELECT column_name, data_type, character_maximum_length
                FROM information_schema.columns 
                WHERE table_name = 'accounts'
                ORDER BY ordinal_position
            """)
            result = await db.execute(columns_query)
            for row in result.fetchall():
                col_name, data_type, max_length = row
                length_info = f" ({max_length})" if max_length else ""
                print(f"    {col_name}: {data_type}{length_info}")
        
        if 'subscriptions' in tables:
            print("\n📋 COLUMNAS DE SUBSCRIPTIONS:")
            columns_query = text("""
                SELECT column_name, data_type, character_maximum_length
                FROM information_schema.columns 
                WHERE table_name = 'subscriptions'
                ORDER BY ordinal_position
            """)
            result = await db.execute(columns_query)
            for row in result.fetchall():
                col_name, data_type, max_length = row
                length_info = f" ({max_length})" if max_length else ""
                print(f"    {col_name}: {data_type}{length_info}")
        
    finally:
        await db.close()


if __name__ == "__main__":
    asyncio.run(check_existing_tables())
