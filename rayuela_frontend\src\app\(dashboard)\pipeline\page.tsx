"use client";

// Removed unused imports: useState, useEffect
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import {
  Zap,
  Database,
  Brain,
  Play,
  Upload,
  RefreshCw,
  AlertCircle,
  History,
  Settings,
  Cpu,
  Server
} from 'lucide-react';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import Link from 'next/link';
import { getStatusBadge } from '@/lib/utils/format';
import { useTrainingJobs, useIngestionJobs, useModels } from '@/lib/hooks';
import { DataIngestionModal } from '@/components/pipeline/DataIngestionModal';
import { TrainingModal } from '@/components/pipeline/TrainingModal';

// Mock functions removed - now using real API hooks!

export default function PipelinePage() {
  // Use real hooks instead of mock data
  const { jobs: trainingJobs, isLoading: trainingLoading, error: trainingError, startTraining } = useTrainingJobs();
  const { jobs: ingestionJobs, isLoading: ingestionLoading, error: ingestionError, startBatchIngestion } = useIngestionJobs();
  const { models, isLoading: modelsLoading, error: modelsError } = useModels();

  // Get the most recent jobs
  const lastTraining = trainingJobs.length > 0 ? trainingJobs[0] : null;
  const lastIngestion = ingestionJobs.length > 0 ? ingestionJobs[0] : null;
  const activeModels = models.filter(model => model.artifact_name); // Filter active models

  // Combine loading states
  const isLoading = trainingLoading || ingestionLoading || modelsLoading;

  // Combine error states
  const error = trainingError || ingestionError || modelsError;

  /* Función para obtener iconos de estado (no usada actualmente)
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return <CheckCircle className="h-4 w-4 text-success" />;
      case 'PROCESSING':
        return <Activity className="h-4 w-4 text-info animate-pulse" />;
      case 'PENDING':
        return <Clock className="h-4 w-4 text-warning" />;
      case 'FAILED':
        return <XCircle className="h-4 w-4 text-destructive" />;
      default:
        return <AlertCircle className="h-4 w-4 text-muted-foreground" />;
    }
  };
  */

  if (isLoading) {
    return (
      <div className="container mx-auto py-8 space-y-8">
        <div className="bg-card/50 border border-border/50 rounded-lg p-6">
          <Skeleton className="h-8 w-64 mb-2" />
          <Skeleton className="h-4 w-96" />
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={`skeleton-${i}`}>
              <CardHeader>
                <Skeleton className="h-6 w-48" />
                <Skeleton className="h-4 w-32" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-20 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-8">
      {/* Header Section */}
      <div className="bg-card/50 border border-border/50 rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold flex items-center gap-3">
              <Cpu className="h-8 w-8 text-primary" />
              Pipeline & Modelos
            </h1>
            <p className="text-muted-foreground mt-2">
              Monitorea tus pipelines de datos y modelos de recomendación
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={() => window.location.reload()}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Actualizar
            </Button>
          </div>
        </div>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Main Cards Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-4 gap-6">
        
        {/* Last Training Job Card */}
        <Card className="col-span-1 xl:col-span-1">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-lg">
              <Brain className="h-5 w-5 text-purple-500" />
              Último Entrenamiento
            </CardTitle>
            <CardDescription>Estado del entrenamiento más reciente</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {lastTraining ? (
              <>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Job #{lastTraining.id}</span>
                  {getStatusBadge(lastTraining.status)}
                </div>
                
                {lastTraining.modelName && (
                  <div className="text-sm text-muted-foreground">
                    {lastTraining.modelName} v{lastTraining.modelVersion}
                  </div>
                )}

                {lastTraining.status === 'PROCESSING' && lastTraining.progress && (
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Progreso</span>
                      <span>{lastTraining.progress}%</span>
                    </div>
                    <Progress value={lastTraining.progress} className="h-2" />
                  </div>
                )}

                <div className="text-xs text-muted-foreground">
                  Inicio: {format(new Date(lastTraining.createdAt), "d 'de' MMM, HH:mm", { locale: es })}
                </div>

                {lastTraining.status === 'FAILED' && lastTraining.errorMessage && (
                  <Alert variant="destructive" className="mt-3">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription className="text-xs">
                      {lastTraining.errorMessage}
                    </AlertDescription>
                  </Alert>
                )}
              </>
            ) : (
              <div className="text-center text-muted-foreground py-4">
                <Brain className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">No hay entrenamientos recientes</p>
              </div>
            )}
            
            <Button variant="outline" size="sm" className="w-full mt-4" asChild>
              <Link href="/pipeline/training-jobs">
                <History className="h-4 w-4 mr-2" />
                Ver Historial
              </Link>
            </Button>
          </CardContent>
        </Card>

        {/* Last Data Ingestion Card */}
        <Card className="col-span-1 xl:col-span-1">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-lg">
              <Database className="h-5 w-5 text-green-500" />
              Ingesta de Datos Reciente
            </CardTitle>
            <CardDescription>Último proceso de carga de datos</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {lastIngestion ? (
              <>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Job #{lastIngestion.id}</span>
                  {getStatusBadge(lastIngestion.status)}
                </div>

                {lastIngestion.recordsProcessed && (
                  <div className="space-y-2">
                    <div className="text-xs font-medium text-muted-foreground">Registros procesados:</div>
                    <div className="grid grid-cols-1 gap-1 text-sm">
                      {lastIngestion.recordsProcessed.users && (
                        <div className="flex justify-between">
                          <span>Usuarios:</span>
                          <span className="font-medium">{lastIngestion.recordsProcessed.users.toLocaleString()}</span>
                        </div>
                      )}
                      {lastIngestion.recordsProcessed.products && (
                        <div className="flex justify-between">
                          <span>Productos:</span>
                          <span className="font-medium">{lastIngestion.recordsProcessed.products.toLocaleString()}</span>
                        </div>
                      )}
                      {lastIngestion.recordsProcessed.interactions && (
                        <div className="flex justify-between">
                          <span>Interacciones:</span>
                          <span className="font-medium">{lastIngestion.recordsProcessed.interactions.toLocaleString()}</span>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                <div className="text-xs text-muted-foreground">
                  {format(new Date(lastIngestion.createdAt), "d 'de' MMM, HH:mm", { locale: es })}
                </div>
              </>
            ) : (
              <div className="text-center text-muted-foreground py-4">
                <Database className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">No hay ingestas recientes</p>
              </div>
            )}

            <Button variant="outline" size="sm" className="w-full mt-4" asChild>
              <Link href="/pipeline/ingestion-jobs">
                <History className="h-4 w-4 mr-2" />
                Ver Historial
              </Link>
            </Button>
          </CardContent>
        </Card>

        {/* Active Models Card */}
        <Card className="col-span-1 xl:col-span-1">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-lg">
              <Server className="h-5 w-5 text-blue-500" />
              Modelos Activos
            </CardTitle>
            <CardDescription>Modelos actualmente en producción</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {activeModels.length > 0 ? (
              <div className="space-y-3">
                {activeModels.slice(0, 2).map((model) => (
                  <div key={model.id} className="border rounded-lg p-3 space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="font-medium text-sm">{model.artifact_name}</span>
                      <Badge variant="success" className="text-xs">v{model.artifact_version}</Badge>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      Entrenado: {format(new Date(model.training_date), "d MMM yyyy", { locale: es })}
                    </div>
                    {model.performance_metrics && (
                      <div className="flex gap-2 text-xs">
                        {Object.entries(model.performance_metrics).map(([key, value]) => (
                          <span key={key} className="bg-muted px-2 py-1 rounded">
                            {key}: {typeof value === 'number' ? (value * 100).toFixed(1) + '%' : value}
                          </span>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
                {activeModels.length > 2 && (
                  <div className="text-xs text-muted-foreground text-center">
                    +{activeModels.length - 2} modelos más
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center text-muted-foreground py-4">
                <Server className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">No hay modelos activos</p>
              </div>
            )}

            <Button variant="outline" size="sm" className="w-full mt-4" asChild>
              <Link href="/models">
                <Settings className="h-4 w-4 mr-2" />
                Gestionar Modelos
              </Link>
            </Button>
          </CardContent>
        </Card>

        {/* Quick Actions Card */}
        <Card className="col-span-1 xl:col-span-1">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-lg">
              <Zap className="h-5 w-5 text-yellow-500" />
              Acciones Rápidas
            </CardTitle>
            <CardDescription>Operaciones comunes del pipeline</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <TrainingModal
              onTrainingStart={startTraining}
              trigger={
                <Button size="sm" className="w-full justify-start">
                  <Play className="h-4 w-4 mr-2" />
                  Iniciar Nuevo Entrenamiento
                </Button>
              }
            />

            <DataIngestionModal
              onIngestionStart={startBatchIngestion}
              trigger={
                <Button variant="outline" size="sm" className="w-full justify-start">
                  <Upload className="h-4 w-4 mr-2" />
                  Subir Datos
                </Button>
              }
            />

            <Button
              variant="outline"
              size="sm"
              className="w-full justify-start"
              onClick={() => window.location.reload()}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Actualizar Datos
            </Button>

            <div className="pt-2">
              <div className="text-xs text-muted-foreground">
                Acciones rápidas para gestionar tu pipeline de datos y modelos
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Information Section */}
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Información sobre el Pipeline</AlertTitle>
        <AlertDescription>
          <div className="space-y-2 text-sm mt-2">
            <p>
              Esta sección te permite monitorear el estado de tus procesos de machine learning:
            </p>
            <ul className="list-disc list-inside space-y-1 pl-2">
              <li><strong>Entrenamientos:</strong> Seguimiento de jobs de entrenamiento de modelos</li>
              <li><strong>Ingesta:</strong> Monitoreo de procesos de carga de datos</li>
              <li><strong>Modelos:</strong> Estado de modelos activos en producción</li>
              <li><strong>Métricas:</strong> Rendimiento y estado de salud del pipeline</li>
            </ul>
            <p className="mt-2 text-muted-foreground">
              <strong>Próximamente:</strong> Interfaces para iniciar entrenamientos, 
              gestionar datos y configurar parámetros de modelo.
            </p>
          </div>
        </AlertDescription>
      </Alert>
    </div>
  );
} 