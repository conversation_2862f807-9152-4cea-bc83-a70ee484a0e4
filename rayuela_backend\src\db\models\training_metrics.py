from datetime import datetime, timezone
from sqlalchemy import Column, Integer, Float, ForeignKey, DateTime, ForeignKeyConstraint, PrimaryKeyConstraint, Index, Identity
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship
from src.db.base import Base
from src.db.models.mixins import TenantMixin, ACCOUNT_ID_FK, get_tenant_table_args


class TrainingMetrics(Base, TenantMixin):
    """
    Modelo para almacenar métricas de entrenamiento de modelos.
    """
    __tablename__ = "training_metrics"

    account_id = Column(Integer, ForeignKey(ACCOUNT_ID_FK), primary_key=True)
    id = Column(Integer, Identity(), primary_key=True)
    model_id = Column(Integer, nullable=True)
    timestamp = Column(DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)

    # Métricas de entrenamiento
    accuracy = Column(Float, nullable=True)
    precision = Column(Float, nullable=True)
    recall = Column(Float, nullable=True)
    f1 = Column(Float, nullable=True)

    # Métricas de proceso
    training_time = Column(Float, nullable=True)
    model_size_mb = Column(Float, nullable=True)

    # Datos adicionales
    additional_metrics = Column(JSONB, nullable=True)

    __table_args__ = get_tenant_table_args(
        PrimaryKeyConstraint("account_id", "id"),
        # Composite FK for proper tenant isolation
        ForeignKeyConstraint(
            ["account_id", "model_id"],
            ["model_metadata.account_id", "model_metadata.id"],
            ondelete="CASCADE",
            name="fk_training_metrics_model"
        ),
        Index("idx_training_metrics_model", "account_id", "model_id"),
        Index("idx_training_metrics_timestamp", "timestamp")
    )

    # Relaciones
    account = relationship("Account", back_populates="training_metrics", overlaps="training_metrics")
    model = relationship(
        "ModelMetadata",
        foreign_keys=[account_id, model_id],
        primaryjoin="and_(TrainingMetrics.account_id==ModelMetadata.account_id, TrainingMetrics.model_id==ModelMetadata.id)",
        back_populates="training_metrics",
        overlaps="account,training_metrics",
    )

    def __repr__(self):
        return f"<TrainingMetrics(id={self.id}, model_id={self.model_id}, accuracy={self.accuracy})>"