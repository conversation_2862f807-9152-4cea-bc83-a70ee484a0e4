from sqlalchemy import (
    Column,
    Integer,
    DateTime,
    ForeignKey,
    Text,
    Index,
    func,
    ForeignKeyConstraint,
    PrimaryKeyConstraint,
    Identity,
)
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship
from src.db.base import Base
from .mixins import TenantMixin, ACCOUNT_ID_FK, get_tenant_table_args


class Search(Base, TenantMixin):
    __tablename__ = "searches"

    account_id = Column(Integer, ForeignKey(ACCOUNT_ID_FK), primary_key=True)
    id = Column(Integer, Identity(), primary_key=True)
    user_id = Column(Integer, nullable=False)
    query = Column(Text)
    timestamp = Column(DateTime(timezone=True), server_default=func.now())
    results_metadata = Column(JSONB, nullable=True, comment="Metadatos sobre los resultados de la búsqueda (ej. IDs de productos, total de resultados)")

    __table_args__ = get_tenant_table_args(
        PrimaryKeyConstraint("account_id", "id"),
        # Composite FK for proper tenant isolation
        ForeignKeyConstraint(
            ["account_id", "user_id"],
            ["end_users.account_id", "end_users.user_id"],
            ondelete="CASCADE",
            name="fk_search_user"
        ),
        Index("idx_search_account_user", "account_id", "user_id"),
        Index("idx_search_account_timestamp", "account_id", "timestamp")
    )

    # Relationships
    account = relationship("Account", back_populates="searches", overlaps="searches")
    end_user = relationship(
        "EndUser",
        foreign_keys=[user_id],
        primaryjoin="and_(Search.account_id==EndUser.account_id, Search.user_id==EndUser.user_id)",
        back_populates="searches"
    )
