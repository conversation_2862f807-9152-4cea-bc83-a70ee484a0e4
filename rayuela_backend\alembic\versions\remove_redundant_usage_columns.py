"""Remove redundant usage tracking columns from subscriptions

Revision ID: remove_redundant_usage_columns
Revises: previous_revision
Create Date: 2024-01-XX XX:XX:XX.XXXXXX
"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'remove_redundant_usage_columns'
down_revision = None  # Replace with actual previous revision
branch_labels = None
depends_on = None

def upgrade() -> None:
    """Remove redundant usage tracking columns."""
    print("🔧 Removing redundant usage tracking columns...")
    
    # Migrate any data from current_period columns to main columns if needed
    op.execute("""
        UPDATE subscriptions 
        SET monthly_api_calls_used = COALESCE(api_calls_used_current_period, monthly_api_calls_used),
            storage_used = COALESCE(storage_used_current_period, storage_used)
        WHERE api_calls_used_current_period IS NOT NULL 
           OR storage_used_current_period IS NOT NULL
    """)
    
    # Drop redundant columns
    op.drop_column('subscriptions', 'api_calls_used_current_period')
    op.drop_column('subscriptions', 'storage_used_current_period')
    op.drop_column('subscriptions', 'period_start')
    op.drop_column('subscriptions', 'period_end')
    
    print("✅ Successfully removed redundant usage columns")

def downgrade() -> None:
    """Re-add the removed columns."""
    op.add_column('subscriptions', sa.Column('api_calls_used_current_period', sa.Integer(), default=0))
    op.add_column('subscriptions', sa.Column('storage_used_current_period', sa.BigInteger(), default=0))
    op.add_column('subscriptions', sa.Column('period_start', sa.DateTime(timezone=True), nullable=True))
    op.add_column('subscriptions', sa.Column('period_end', sa.DateTime(timezone=True), nullable=True))