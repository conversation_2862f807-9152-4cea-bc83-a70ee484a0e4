"use client";

import { useState, useEffect } from "react";
import { CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { DensePageLayout, DenseCard } from "@/components/ui/layout";
import { InfoIcon, ExternalLinkIcon, BrainCircuit, Calendar, Gauge, Settings } from "lucide-react";
import { useModels, type ModelInfo } from "@/lib/hooks/useModels";
import { format } from "date-fns";
import { es } from "date-fns/locale";

export default function ModelsPage() {
  const { models, isLoading, error } = useModels();

  // Estado para el error
  const [errorState, setErrorState] = useState<string | null>(null);

  // Actualizar el estado de error cuando cambia error
  useEffect(() => {
    if (error) {
      console.error("Error al obtener datos de modelos:", error);
      const errorMessage = typeof error === 'string' 
        ? error 
        : "Error al cargar los datos de modelos";
      setErrorState(errorMessage);
    } else {
      setErrorState(null);
    }
  }, [error]);

  // Function to render trained models
  const renderTrainedModels = () => {
    if (isLoading) {
      return (
        <div className="space-y-4">
          <Skeleton className="h-24 w-full" />
          <Skeleton className="h-24 w-full" />
          <Skeleton className="h-24 w-full" />
        </div>
      );
    }

    if (errorState) {
      return (
        <Alert variant="destructive">
          <AlertTitle>Error cargando modelos</AlertTitle>
          <AlertDescription>{errorState}</AlertDescription>
        </Alert>
      );
    }

    if (models.length === 0) {
      return (
        <div className="flex flex-col items-center justify-center h-40 border-2 border-dashed rounded-md border-border p-4">
          <BrainCircuit className="h-8 w-8 text-muted-foreground mb-2" />
          <p className="text-muted-foreground text-center">
            No hay modelos entrenados aún
          </p>
          <p className="text-sm text-muted-foreground text-center">
            Los modelos aparecerán aquí después de completar un entrenamiento
          </p>
        </div>
      );
    }

    return (
      <div className="space-y-4">
        {models.map((model: ModelInfo, index) => {
          const isActive = index === 0; // First model is considered active

          return (
            <div key={model.id} className="p-4 border border-border rounded-lg hover:bg-muted/50 transition-colors">
              <div className="flex items-start justify-between">
                <div className="flex-grow">
                  <div className="flex items-center gap-3 mb-2">
                    <BrainCircuit className="h-5 w-5 text-primary" />
                    <h3 className="font-semibold text-foreground">
                      {model.artifact_name}
                    </h3>
                    <Badge variant="secondary">
                      {model.artifact_version}
                    </Badge>
                    {isActive && (
                      <Badge variant="success">Activo</Badge>
                    )}
                  </div>

                  {model.description && (
                    <p className="text-sm text-muted-foreground mb-3">
                      {model.description}
                    </p>
                  )}

                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      <span>
                        Entrenado: {format(new Date(model.training_date), 'dd/MM/yyyy HH:mm', { locale: es })}
                      </span>
                    </div>

                    {model.performance_metrics && Object.keys(model.performance_metrics).length > 0 && (
                      <div className="flex items-center gap-1">
                        <Gauge className="h-4 w-4" />
                        <span>
                          {Object.entries(model.performance_metrics).slice(0, 2).map(([key, value]) =>
                            `${key}: ${typeof value === 'number' ? value.toFixed(3) : value}`
                          ).join(', ')}
                        </span>
                      </div>
                    )}
                  </div>

                  {model.parameters && Object.keys(model.parameters).length > 0 && (
                    <div className="mt-2 text-xs text-muted-foreground">
                      <strong>Parámetros:</strong> {Object.entries(model.parameters).slice(0, 3).map(([key, value]) =>
                        `${key}: ${value}`
                      ).join(', ')}
                    </div>
                  )}
                </div>

                <div className="flex flex-col gap-2">
                  {!isActive && (
                    <Button variant="outline" size="sm" disabled>
                      Activar
                    </Button>
                  )}
                  <Button variant="ghost" size="sm" disabled>
                    Ver Detalles
                  </Button>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  // Find the active model (most recent one)
  const activeModel = models.length > 0 ? models[0] : null;

  return (
    <DensePageLayout
      title="Modelos de Recomendación"
      description="Gestiona y configura tus modelos de recomendación personalizados."
    >
      {/* Active Model Section */}
      {activeModel ? (
        <DenseCard
          title="Modelo Activo"
          description="Tu modelo de recomendación actualmente en producción"
          icon={<BrainCircuit className="h-5 w-5 text-green-500" />}
          headerActions={
            <Button variant="outline" size="sm">
              <Settings className="h-4 w-4 mr-2" />
              Configurar
            </Button>
          }
        >
          <CardContent className="space-y-4">
            <div className="flex items-start justify-between">
              <div className="space-y-2">
                <div className="flex items-center gap-3">
                  <h3 className="text-lg font-semibold">{activeModel.artifact_name}</h3>
                  <Badge variant="success">Activo</Badge>
                  <Badge variant="secondary">{activeModel.artifact_version}</Badge>
                </div>
                {activeModel.description && (
                  <p className="text-sm text-muted-foreground">{activeModel.description}</p>
                )}
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <Calendar className="h-4 w-4" />
                    <span>Entrenado: {format(new Date(activeModel.training_date), 'dd/MM/yyyy HH:mm', { locale: es })}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Performance Metrics */}
            {activeModel.performance_metrics && Object.keys(activeModel.performance_metrics).length > 0 && (
              <div className="space-y-3">
                <h4 className="font-medium flex items-center gap-2">
                  <Gauge className="h-4 w-4" />
                  Métricas de Rendimiento
                </h4>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  {Object.entries(activeModel.performance_metrics).slice(0, 4).map(([key, value]) => (
                    <div key={key} className="bg-muted/50 p-3 rounded-lg">
                      <div className="text-xs font-medium text-muted-foreground uppercase">{key}</div>
                      <div className="text-lg font-semibold">
                        {typeof value === 'number' ? value.toFixed(3) : value}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </DenseCard>
      ) : (
        <Alert>
          <InfoIcon className="h-4 w-4" />
          <AlertTitle>No hay modelos entrenados</AlertTitle>
          <AlertDescription>
            Inicia tu primer entrenamiento para ver tus modelos aquí.
          </AlertDescription>
        </Alert>
      )}

      {/* Models History Section */}
      <DenseCard
        title="Historial de Modelos"
        description="Todos tus modelos entrenados y sus métricas de rendimiento"
        icon={<BrainCircuit className="h-5 w-5" />}
        headerActions={
          <Button asChild variant="outline" size="sm">
            <a
              href="https://docs.rayuela.ai/models/overview"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center"
            >
              <ExternalLinkIcon className="mr-2 h-4 w-4" />
              Ver documentación
            </a>
          </Button>
        }
      >
        <CardContent>
          {renderTrainedModels()}
        </CardContent>
      </DenseCard>
    </DensePageLayout>
  );
}
