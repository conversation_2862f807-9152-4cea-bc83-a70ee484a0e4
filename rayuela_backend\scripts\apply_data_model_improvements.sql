-- Script SQL para aplicar mejoras del modelo de datos
-- Ejecutar este script directamente en la base de datos PostgreSQL

-- ============================================================================
-- MEJORAS DEL MODELO DE DATOS - RAYUELA
-- ============================================================================

BEGIN;

-- Mostrar progreso
\echo '🚀 Iniciando mejoras del modelo de datos...'

-- ============================================================================
-- 1. CONVERTIR JSON A JSONB PARA MEJOR RENDIMIENTO
-- ============================================================================

\echo '📊 Convirtiendo columnas JSON a JSONB...'

-- Experiment model
DO $$
BEGIN
    -- control_config
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'experiments' 
        AND column_name = 'control_config' 
        AND data_type = 'json'
    ) THEN
        ALTER TABLE experiments ALTER COLUMN control_config TYPE JSONB USING control_config::jsonb;
        \echo '  ✅ experiments.control_config: JSON → JSONB'
    ELSE
        \echo '  ⏭️ experiments.control_config: Ya es JSONB'
    END IF;

    -- treatment_config
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'experiments' 
        AND column_name = 'treatment_config' 
        AND data_type = 'json'
    ) THEN
        ALTER TABLE experiments ALTER COLUMN treatment_config TYPE JSONB USING treatment_config::jsonb;
        \echo '  ✅ experiments.treatment_config: JSON → JSONB'
    ELSE
        \echo '  ⏭️ experiments.treatment_config: Ya es JSONB'
    END IF;

    -- tags
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'experiments' 
        AND column_name = 'tags' 
        AND data_type = 'json'
    ) THEN
        ALTER TABLE experiments ALTER COLUMN tags TYPE JSONB USING tags::jsonb;
        \echo '  ✅ experiments.tags: JSON → JSONB'
    ELSE
        \echo '  ⏭️ experiments.tags: Ya es JSONB'
    END IF;
END $$;

-- ExperimentResult model
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'experiment_results' 
        AND column_name = 'additional_metrics' 
        AND data_type = 'json'
    ) THEN
        ALTER TABLE experiment_results ALTER COLUMN additional_metrics TYPE JSONB USING additional_metrics::jsonb;
        \echo '  ✅ experiment_results.additional_metrics: JSON → JSONB'
    ELSE
        \echo '  ⏭️ experiment_results.additional_metrics: Ya es JSONB'
    END IF;
END $$;

-- ExperimentEvent model
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'experiment_events' 
        AND column_name = 'additional_context' 
        AND data_type = 'json'
    ) THEN
        ALTER TABLE experiment_events ALTER COLUMN additional_context TYPE JSONB USING additional_context::jsonb;
        \echo '  ✅ experiment_events.additional_context: JSON → JSONB'
    ELSE
        \echo '  ⏭️ experiment_events.additional_context: Ya es JSONB'
    END IF;
END $$;

-- ModelMetric model
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'model_metrics' 
        AND column_name = 'additional_metrics' 
        AND data_type = 'json'
    ) THEN
        ALTER TABLE model_metrics ALTER COLUMN additional_metrics TYPE JSONB USING additional_metrics::jsonb;
        \echo '  ✅ model_metrics.additional_metrics: JSON → JSONB'
    ELSE
        \echo '  ⏭️ model_metrics.additional_metrics: Ya es JSONB'
    END IF;
END $$;

-- TrainingJob model
DO $$
BEGIN
    -- parameters
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'training_jobs' 
        AND column_name = 'parameters' 
        AND data_type = 'json'
    ) THEN
        ALTER TABLE training_jobs ALTER COLUMN parameters TYPE JSONB USING parameters::jsonb;
        \echo '  ✅ training_jobs.parameters: JSON → JSONB'
    ELSE
        \echo '  ⏭️ training_jobs.parameters: Ya es JSONB'
    END IF;

    -- metrics
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'training_jobs' 
        AND column_name = 'metrics' 
        AND data_type = 'json'
    ) THEN
        ALTER TABLE training_jobs ALTER COLUMN metrics TYPE JSONB USING metrics::jsonb;
        \echo '  ✅ training_jobs.metrics: JSON → JSONB'
    ELSE
        \echo '  ⏭️ training_jobs.metrics: Ya es JSONB'
    END IF;
END $$;

-- TrainingMetrics model
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'training_metrics' 
        AND column_name = 'additional_metrics' 
        AND data_type = 'json'
    ) THEN
        ALTER TABLE training_metrics ALTER COLUMN additional_metrics TYPE JSONB USING additional_metrics::jsonb;
        \echo '  ✅ training_metrics.additional_metrics: JSON → JSONB'
    ELSE
        \echo '  ⏭️ training_metrics.additional_metrics: Ya es JSONB'
    END IF;
END $$;

-- ============================================================================
-- 2. AÑADIR COLUMNA results_metadata AL MODELO Search
-- ============================================================================

\echo '🔍 Añadiendo columna results_metadata a searches...'

DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'searches' 
        AND column_name = 'results_metadata'
    ) THEN
        ALTER TABLE searches ADD COLUMN results_metadata JSONB;
        COMMENT ON COLUMN searches.results_metadata IS 'Metadatos sobre los resultados de la búsqueda (ej. IDs de productos, total de resultados)';
        \echo '  ✅ searches.results_metadata: Columna añadida'
    ELSE
        \echo '  ⏭️ searches.results_metadata: Ya existe'
    END IF;
END $$;

-- ============================================================================
-- 3. OPTIMIZAR LONGITUDES DE CAMPOS MERCADOPAGO
-- ============================================================================

\echo '💳 Optimizando campos de MercadoPago...'

-- Account model
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'accounts' 
        AND column_name = 'mercadopago_customer_id' 
        AND character_maximum_length = 255
    ) THEN
        ALTER TABLE accounts ALTER COLUMN mercadopago_customer_id TYPE VARCHAR(20);
        \echo '  ✅ accounts.mercadopago_customer_id: 255 → 20 caracteres'
    ELSE
        \echo '  ⏭️ accounts.mercadopago_customer_id: Ya optimizado'
    END IF;
END $$;

-- Subscription model
DO $$
BEGIN
    -- mercadopago_subscription_id
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'subscriptions' 
        AND column_name = 'mercadopago_subscription_id' 
        AND character_maximum_length = 255
    ) THEN
        ALTER TABLE subscriptions ALTER COLUMN mercadopago_subscription_id TYPE VARCHAR(20);
        \echo '  ✅ subscriptions.mercadopago_subscription_id: 255 → 20 caracteres'
    ELSE
        \echo '  ⏭️ subscriptions.mercadopago_subscription_id: Ya optimizado'
    END IF;

    -- mercadopago_price_id
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'subscriptions' 
        AND column_name = 'mercadopago_price_id' 
        AND character_maximum_length = 255
    ) THEN
        ALTER TABLE subscriptions ALTER COLUMN mercadopago_price_id TYPE VARCHAR(20);
        \echo '  ✅ subscriptions.mercadopago_price_id: 255 → 20 caracteres'
    ELSE
        \echo '  ⏭️ subscriptions.mercadopago_price_id: Ya optimizado'
    END IF;
END $$;

-- ============================================================================
-- 4. AÑADIR COMENTARIOS DE DEPRECACIÓN A CAMPOS DE SUBSCRIPTION
-- ============================================================================

\echo '📝 Añadiendo comentarios de deprecación...'

COMMENT ON COLUMN subscriptions.monthly_api_calls_used IS 'DEPRECATED: Use AccountUsageMetrics.billing_period_api_calls';
COMMENT ON COLUMN subscriptions.storage_used IS 'DEPRECATED: Use AccountUsageMetrics.billing_period_storage';
COMMENT ON COLUMN subscriptions.last_reset_date IS 'DEPRECATED: Use AccountUsageMetrics.last_billing_cycle';

\echo '  ✅ Comentarios de deprecación añadidos'

-- ============================================================================
-- 5. VERIFICACIÓN FINAL
-- ============================================================================

\echo '🔍 Verificando cambios aplicados...'

-- Verificar conversiones JSONB
SELECT 
    table_name,
    column_name,
    data_type,
    CASE 
        WHEN data_type = 'jsonb' THEN '✅ JSONB'
        WHEN data_type = 'json' THEN '❌ JSON'
        ELSE data_type
    END as status
FROM information_schema.columns 
WHERE table_name IN ('experiments', 'experiment_results', 'experiment_events', 'model_metrics', 'training_jobs', 'training_metrics')
AND column_name IN ('control_config', 'treatment_config', 'tags', 'additional_metrics', 'additional_context', 'parameters', 'metrics')
ORDER BY table_name, column_name;

-- Verificar campo results_metadata
SELECT 
    table_name,
    column_name,
    data_type,
    '✅ Añadido' as status
FROM information_schema.columns 
WHERE table_name = 'searches' 
AND column_name = 'results_metadata';

-- Verificar optimización MercadoPago
SELECT 
    table_name,
    column_name,
    character_maximum_length,
    CASE 
        WHEN character_maximum_length = 20 THEN '✅ Optimizado'
        WHEN character_maximum_length = 255 THEN '⚠️ Pendiente'
        ELSE 'N/A'
    END as status
FROM information_schema.columns 
WHERE (table_name = 'accounts' AND column_name = 'mercadopago_customer_id')
   OR (table_name = 'subscriptions' AND column_name IN ('mercadopago_subscription_id', 'mercadopago_price_id'))
ORDER BY table_name, column_name;

COMMIT;

\echo '🎉 ¡Mejoras del modelo de datos aplicadas exitosamente!'
\echo ''
\echo '📋 PRÓXIMOS PASOS:'
\echo '1. Ejecutar migración de métricas de uso: python scripts/migrate_usage_metrics.py'
\echo '2. Verificar funcionamiento en aplicación'
\echo '3. Monitorear rendimiento de consultas JSONB'
