{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/globals.css"], "sourcesContent": ["/* === Animaciones utilitarias === */\r\n@import \"tw-animate-css\";\r\n\r\n/* === Tailwind layers: declara una sola vez === */\r\n@tailwind base;\r\n@tailwind components;\r\n@tailwind utilities;\r\n\r\n/* =========================================================\r\n   TOKENS & THEME\r\n   =======================================================*/\r\n@custom-variant dark (&:is(.dark *));\r\n\r\n@theme inline {\r\n  --color-background: var(--background);\r\n  --color-foreground: var(--foreground);\r\n  --font-sans: var(--font-geist-sans);\r\n  --font-mono: var(--font-geist-mono);\r\n  --color-sidebar-ring: var(--sidebar-ring);\r\n  --color-sidebar-border: var(--sidebar-border);\r\n  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);\r\n  --color-sidebar-accent: var(--sidebar-accent);\r\n  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);\r\n  --color-sidebar-primary: var(--sidebar-primary);\r\n  --color-sidebar-foreground: var(--sidebar-foreground);\r\n  --color-sidebar: var(--sidebar);\r\n  --color-chart-5: var(--chart-5);\r\n  --color-chart-4: var(--chart-4);\r\n  --color-chart-3: var(--chart-3);\r\n  --color-chart-2: var(--chart-2);\r\n  --color-chart-1: var(--chart-1);\r\n  --color-ring: var(--ring);\r\n  --color-input: var(--input);\r\n  --color-border: var(--border);\r\n  --color-destructive: var(--destructive);\r\n  --color-accent-foreground: var(--accent-foreground);\r\n  --color-accent: var(--accent);\r\n  --color-muted-foreground: var(--muted-foreground);\r\n  --color-muted: var(--muted);\r\n  --color-secondary-foreground: var(--secondary-foreground);\r\n  --color-secondary: var(--secondary);\r\n  --color-primary-foreground: var(--primary-foreground);\r\n  --color-primary: var(--primary);\r\n  --color-popover-foreground: var(--popover-foreground);\r\n  --color-popover: var(--popover);\r\n  --color-card-foreground: var(--card-foreground);\r\n  --color-card: var(--card);\r\n  --radius-sm: calc(var(--radius) - 4px);\r\n  --radius-md: calc(var(--radius) - 2px);\r\n  --radius-lg: var(--radius);\r\n  --radius-xl: calc(var(--radius) + 4px);\r\n}\r\n\r\n:root {\r\n  --radius: 0.625rem;\r\n  --background: oklch(1 0 0);\r\n  --foreground: oklch(0.129 0.042 264.695);\r\n  --card: oklch(1 0 0);\r\n  --card-foreground: oklch(0.129 0.042 264.695);\r\n  --popover: oklch(1 0 0);\r\n  --popover-foreground: oklch(0.129 0.042 264.695);\r\n  --primary: 252 86% 53%;\r\n  --primary-foreground: 0 0% 100%;\r\n  --secondary: oklch(0.968 0.007 247.896);\r\n  --secondary-foreground: oklch(0.208 0.042 265.755);\r\n  --muted: oklch(0.968 0.007 247.896);\r\n  --muted-foreground: oklch(0.554 0.046 257.417);\r\n  --accent: 284 62% 57%;\r\n  --accent-foreground: 0 0% 100%;\r\n  --destructive: oklch(0.577 0.245 27.325);\r\n  --border: oklch(0.929 0.013 255.508);\r\n  --input: oklch(0.929 0.013 255.508);\r\n  --ring: oklch(0.704 0.04 256.788);\r\n  --chart-1: oklch(0.646 0.222 41.116);\r\n  --chart-2: oklch(0.6 0.118 184.704);\r\n  --chart-3: oklch(0.398 0.07 227.392);\r\n  --chart-4: oklch(0.828 0.189 84.429);\r\n  --chart-5: oklch(0.769 0.188 70.08);\r\n  --sidebar: oklch(0.984 0.003 247.858);\r\n  --sidebar-foreground: oklch(0.129 0.042 264.695);\r\n  --sidebar-primary: oklch(0.208 0.042 265.755);\r\n  --sidebar-primary-foreground: oklch(0.984 0.003 247.858);\r\n  --sidebar-accent: oklch(0.968 0.007 247.896);\r\n  --sidebar-accent-foreground: oklch(0.208 0.042 265.755);\r\n  --sidebar-border: oklch(0.929 0.013 255.508);\r\n  --sidebar-ring: oklch(0.704 0.04 256.788);\r\n  \r\n  /* Colores semánticos adicionales armonizados con la paleta oklch */\r\n  --success: oklch(0.646 0.222 145.0);\r\n  --success-foreground: oklch(0.984 0.003 247.858);\r\n  --success-light: oklch(0.97 0.022 145.0);\r\n  --warning: oklch(0.828 0.189 84.429);\r\n  --warning-foreground: oklch(0.129 0.042 264.695);\r\n  --warning-light: oklch(0.97 0.022 84.429);\r\n  --info: oklch(0.6 0.118 220.0);\r\n  --info-foreground: oklch(0.984 0.003 247.858);\r\n  --info-light: oklch(0.97 0.022 220.0);\r\n  \r\n  /* Variables para gradientes de marketing unificados */\r\n  --marketing-gradient-start: oklch(0.984 0.003 247.858);\r\n  --marketing-gradient-end: oklch(0.968 0.007 247.896);\r\n  --marketing-gradient-accent: oklch(0.929 0.013 255.508);\r\n\r\n  /* Variables para gradientes semánticos unificados */\r\n  --gradient-success-start: oklch(0.646 0.222 145.0);\r\n  --gradient-success-end: oklch(0.828 0.189 84.429);\r\n  --gradient-info-start: oklch(0.6 0.118 220.0);\r\n  --gradient-info-end: oklch(0.488 0.243 264.376);\r\n  --gradient-brand-start: oklch(0.704 0.04 256.788);\r\n  --gradient-brand-end: oklch(0.627 0.265 303.9);\r\n}\r\n\r\n.dark {\r\n  --background: oklch(0.129 0.042 264.695);\r\n  --foreground: oklch(0.984 0.003 247.858);\r\n  --card: oklch(0.208 0.042 265.755);\r\n  --card-foreground: oklch(0.984 0.003 247.858);\r\n  --popover: oklch(0.208 0.042 265.755);\r\n  --popover-foreground: oklch(0.984 0.003 247.858);\r\n  --primary: 252 100% 70%;\r\n  --primary-foreground: 0 0% 100%;\r\n  --secondary: oklch(0.279 0.041 260.031);\r\n  --secondary-foreground: oklch(0.984 0.003 247.858);\r\n  --muted: oklch(0.279 0.041 260.031);\r\n  --muted-foreground: oklch(0.704 0.04 256.788);\r\n  --accent: 284 100% 75%;\r\n  --accent-foreground: 0 0% 100%;\r\n  --destructive: oklch(0.704 0.191 22.216);\r\n  --border: oklch(1 0 0 / 10%);\r\n  --input: oklch(1 0 0 / 15%);\r\n  --ring: oklch(0.551 0.027 264.364);\r\n  --chart-1: oklch(0.488 0.243 264.376);\r\n  --chart-2: oklch(0.696 0.17 162.48);\r\n  --chart-3: oklch(0.769 0.188 70.08);\r\n  --chart-4: oklch(0.627 0.265 303.9);\r\n  --chart-5: oklch(0.645 0.246 16.439);\r\n  --sidebar: oklch(0.208 0.042 265.755);\r\n  --sidebar-foreground: oklch(0.984 0.003 247.858);\r\n  --sidebar-primary: oklch(0.488 0.243 264.376);\r\n  --sidebar-primary-foreground: oklch(0.984 0.003 247.858);\r\n  --sidebar-accent: oklch(0.279 0.041 260.031);\r\n  --sidebar-accent-foreground: oklch(0.984 0.003 247.858);\r\n  --sidebar-border: oklch(1 0 0 / 10%);\r\n  --sidebar-ring: oklch(0.551 0.027 264.364);\r\n  \r\n  /* Colores semánticos para modo oscuro */\r\n  --success: oklch(0.696 0.17 145.0);\r\n  --success-foreground: oklch(0.984 0.003 247.858);\r\n  --success-light: oklch(0.279 0.041 145.0);\r\n  --warning: oklch(0.769 0.188 84.429);\r\n  --warning-foreground: oklch(0.984 0.003 247.858);\r\n  --warning-light: oklch(0.279 0.041 84.429);\r\n  --info: oklch(0.488 0.243 220.0);\r\n  --info-foreground: oklch(0.984 0.003 247.858);\r\n  --info-light: oklch(0.279 0.041 220.0);\r\n  \r\n  /* Gradientes de marketing para modo oscuro */\r\n  --marketing-gradient-start: oklch(0.129 0.042 264.695);\r\n  --marketing-gradient-end: oklch(0.208 0.042 265.755);\r\n  --marketing-gradient-accent: oklch(0.279 0.041 260.031);\r\n\r\n  /* Gradientes semánticos para modo oscuro */\r\n  --gradient-success-start: oklch(0.696 0.17 145.0);\r\n  --gradient-success-end: oklch(0.769 0.188 84.429);\r\n  --gradient-info-start: oklch(0.488 0.243 220.0);\r\n  --gradient-info-end: oklch(0.627 0.265 303.9);\r\n  --gradient-brand-start: oklch(0.551 0.027 264.364);\r\n  --gradient-brand-end: oklch(0.488 0.243 264.376);\r\n}\r\n\r\n\r\n/* ==== GRADIENT SECTIONS ==== */\r\n.section-gradient {\r\n  background: linear-gradient(180deg, #fafafa 0%, #ffffff 100%);\r\n}\r\n\r\n/* ==== HERO IMAGE ==== */\r\n.hero-img {\r\n  max-width: 540px;\r\n  height: auto;\r\n  -o-object-fit: contain;\r\n     object-fit: contain;\r\n}\r\n\r\n/* ==== CARDS ==== */\r\n.card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  padding: 1.5rem;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.04);\r\n  transition: all 0.3s ease;\r\n}\r\n.card:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 6px 18px rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n/* ==== STEP CIRCLES ==== */\r\n.step-circle {\r\n  width: 42px;\r\n  height: 42px;\r\n  background-color: #7c3aed; /* Purple-600 */\r\n  color: white;\r\n  font-weight: bold;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin: 0 auto 1rem auto;\r\n  font-size: 1rem;\r\n}\r\n\r\n/* ==== BUTTONS ==== */\r\n.btn-primary {\r\n  display: inline-block;\r\n  padding: 0.75rem 1.5rem;\r\n  background-color: #7c3aed;\r\n  color: white;\r\n  border-radius: 8px;\r\n  font-weight: 600;\r\n  font-size: 0.95rem;\r\n  text-align: center;\r\n  transition: background-color 0.2s ease, transform 0.2s ease;\r\n}\r\n.btn-primary:hover {\r\n  background-color: #6d28d9;\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.btn-secondary {\r\n  display: inline-block;\r\n  padding: 0.75rem 1.5rem;\r\n  background-color: white;\r\n  color: #374151;\r\n  border: 1px solid #e5e7eb;\r\n  border-radius: 8px;\r\n  font-weight: 500;\r\n  font-size: 0.95rem;\r\n  text-align: center;\r\n  transition: all 0.2s ease;\r\n}\r\n.btn-secondary:hover {\r\n  background-color: #f9fafb;\r\n  border-color: #d1d5db;\r\n  transform: translateY(-2px);\r\n}\r\n\r\n/* ==== CODE SNIPPET ==== */\r\npre {\r\n  font-family: 'Fira Code', monospace;\r\n  font-size: 0.9rem;\r\n  line-height: 1.5;\r\n}\r\n\r\n/* ==== TEXT ==== */\r\nh1, h2, h3 {\r\n  font-family: 'Inter', sans-serif;\r\n}\r\np, li, button {\r\n  font-family: 'Inter', sans-serif;\r\n}\r\n\r\n/* ==== RESPONSIVE ==== */\r\n@media (max-width: 768px) {\r\n  .hero-img {\r\n    max-width: 100%;\r\n  }\r\n}\r\n\r\n\r\n/* ==== SECCIONES CON GRADIENTE MARCADO ==== */\r\n.section-gradient-strong {\r\n  background: linear-gradient(180deg, #f5f3ff 0%, #ffffff 100%);\r\n  padding: 3rem 0;\r\n}\r\n\r\n/* ==== HERO IMAGE ==== */\r\n.hero-img {\r\n  max-width: 540px;\r\n  height: auto;\r\n  -o-object-fit: contain;\r\n     object-fit: contain;\r\n}\r\n\r\n/* ==== CARDS ==== */\r\n.card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  padding: 1.5rem;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.04);\r\n  transition: all 0.3s ease;\r\n}\r\n.card:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 6px 18px rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n/* ==== STEP CIRCLES ==== */\r\n.step-circle {\r\n  width: 42px;\r\n  height: 42px;\r\n  background: linear-gradient(135deg, #7c3aed, #9333ea);\r\n  color: white;\r\n  font-weight: bold;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin: 0 auto 1rem auto;\r\n  font-size: 1rem;\r\n  box-shadow: 0 4px 10px rgba(124, 58, 237, 0.2);\r\n}\r\n\r\n/* ==== BUTTONS ==== */\r\n.btn-primary {\r\n  display: inline-block;\r\n  padding: 0.75rem 1.5rem;\r\n  background: linear-gradient(135deg, #7c3aed, #9333ea);\r\n  color: white;\r\n  border-radius: 8px;\r\n  font-weight: 600;\r\n  font-size: 0.95rem;\r\n  text-align: center;\r\n  transition: background-color 0.2s ease, transform 0.2s ease;\r\n}\r\n.btn-primary:hover {\r\n  background: linear-gradient(135deg, #6d28d9, #7e22ce);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.btn-secondary {\r\n  display: inline-block;\r\n  padding: 0.75rem 1.5rem;\r\n  background-color: white;\r\n  color: #374151;\r\n  border: 1px solid #e5e7eb;\r\n  border-radius: 8px;\r\n  font-weight: 500;\r\n  font-size: 0.95rem;\r\n  text-align: center;\r\n  transition: all 0.2s ease;\r\n}\r\n.btn-secondary:hover {\r\n  background-color: #f9fafb;\r\n  border-color: #d1d5db;\r\n  transform: translateY(-2px);\r\n}\r\n\r\n/* ==== CODE SNIPPET ==== */\r\npre {\r\n  font-family: 'Fira Code', monospace;\r\n  font-size: 0.9rem;\r\n  line-height: 1.5;\r\n}\r\n\r\n/* ==== TEXT ==== */\r\nh1, h2, h3 {\r\n  font-family: 'Inter', sans-serif;\r\n}\r\np, li, button {\r\n  font-family: 'Inter', sans-serif;\r\n}\r\n\r\n/* ==== RESPONSIVE ==== */\r\n@media (max-width: 768px) {\r\n  .hero-img {\r\n    max-width: 100%;\r\n  }\r\n}\r\n\r\n\r\n*, ::before, ::after{\r\n  --tw-border-spacing-x: 0;\r\n  --tw-border-spacing-y: 0;\r\n  --tw-translate-x: 0;\r\n  --tw-translate-y: 0;\r\n  --tw-rotate: 0;\r\n  --tw-skew-x: 0;\r\n  --tw-skew-y: 0;\r\n  --tw-scale-x: 1;\r\n  --tw-scale-y: 1;\r\n  --tw-pan-x:  ;\r\n  --tw-pan-y:  ;\r\n  --tw-pinch-zoom:  ;\r\n  --tw-scroll-snap-strictness: proximity;\r\n  --tw-gradient-from-position:  ;\r\n  --tw-gradient-via-position:  ;\r\n  --tw-gradient-to-position:  ;\r\n  --tw-ordinal:  ;\r\n  --tw-slashed-zero:  ;\r\n  --tw-numeric-figure:  ;\r\n  --tw-numeric-spacing:  ;\r\n  --tw-numeric-fraction:  ;\r\n  --tw-ring-inset:  ;\r\n  --tw-ring-offset-width: 0px;\r\n  --tw-ring-offset-color: #fff;\r\n  --tw-ring-color: rgb(59 130 246 / 0.5);\r\n  --tw-ring-offset-shadow: 0 0 #0000;\r\n  --tw-ring-shadow: 0 0 #0000;\r\n  --tw-shadow: 0 0 #0000;\r\n  --tw-shadow-colored: 0 0 #0000;\r\n  --tw-blur:  ;\r\n  --tw-brightness:  ;\r\n  --tw-contrast:  ;\r\n  --tw-grayscale:  ;\r\n  --tw-hue-rotate:  ;\r\n  --tw-invert:  ;\r\n  --tw-saturate:  ;\r\n  --tw-sepia:  ;\r\n  --tw-drop-shadow:  ;\r\n  --tw-backdrop-blur:  ;\r\n  --tw-backdrop-brightness:  ;\r\n  --tw-backdrop-contrast:  ;\r\n  --tw-backdrop-grayscale:  ;\r\n  --tw-backdrop-hue-rotate:  ;\r\n  --tw-backdrop-invert:  ;\r\n  --tw-backdrop-opacity:  ;\r\n  --tw-backdrop-saturate:  ;\r\n  --tw-backdrop-sepia:  ;\r\n  --tw-contain-size:  ;\r\n  --tw-contain-layout:  ;\r\n  --tw-contain-paint:  ;\r\n  --tw-contain-style:  ;\r\n}\r\n\r\n\r\n::backdrop{\r\n  --tw-border-spacing-x: 0;\r\n  --tw-border-spacing-y: 0;\r\n  --tw-translate-x: 0;\r\n  --tw-translate-y: 0;\r\n  --tw-rotate: 0;\r\n  --tw-skew-x: 0;\r\n  --tw-skew-y: 0;\r\n  --tw-scale-x: 1;\r\n  --tw-scale-y: 1;\r\n  --tw-pan-x:  ;\r\n  --tw-pan-y:  ;\r\n  --tw-pinch-zoom:  ;\r\n  --tw-scroll-snap-strictness: proximity;\r\n  --tw-gradient-from-position:  ;\r\n  --tw-gradient-via-position:  ;\r\n  --tw-gradient-to-position:  ;\r\n  --tw-ordinal:  ;\r\n  --tw-slashed-zero:  ;\r\n  --tw-numeric-figure:  ;\r\n  --tw-numeric-spacing:  ;\r\n  --tw-numeric-fraction:  ;\r\n  --tw-ring-inset:  ;\r\n  --tw-ring-offset-width: 0px;\r\n  --tw-ring-offset-color: #fff;\r\n  --tw-ring-color: rgb(59 130 246 / 0.5);\r\n  --tw-ring-offset-shadow: 0 0 #0000;\r\n  --tw-ring-shadow: 0 0 #0000;\r\n  --tw-shadow: 0 0 #0000;\r\n  --tw-shadow-colored: 0 0 #0000;\r\n  --tw-blur:  ;\r\n  --tw-brightness:  ;\r\n  --tw-contrast:  ;\r\n  --tw-grayscale:  ;\r\n  --tw-hue-rotate:  ;\r\n  --tw-invert:  ;\r\n  --tw-saturate:  ;\r\n  --tw-sepia:  ;\r\n  --tw-drop-shadow:  ;\r\n  --tw-backdrop-blur:  ;\r\n  --tw-backdrop-brightness:  ;\r\n  --tw-backdrop-contrast:  ;\r\n  --tw-backdrop-grayscale:  ;\r\n  --tw-backdrop-hue-rotate:  ;\r\n  --tw-backdrop-invert:  ;\r\n  --tw-backdrop-opacity:  ;\r\n  --tw-backdrop-saturate:  ;\r\n  --tw-backdrop-sepia:  ;\r\n  --tw-contain-size:  ;\r\n  --tw-contain-layout:  ;\r\n  --tw-contain-paint:  ;\r\n  --tw-contain-style:  ;\r\n}\r\n\r\n\r\n/* ! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com */\r\n\r\n\r\n/*\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\n*/\r\n\r\n\r\n*,\n::before,\n::after {\n  box-sizing: border-box; /* 1 */\n  border-width: 0; /* 2 */\n  border-style: solid; /* 2 */\n  border-color: #e5e7eb; /* 2 */\n}\r\n\r\n\r\n::before,\n::after {\n  --tw-content: '';\n}\r\n\r\n\r\n/*\n1. Use a consistent sensible line-height in all browsers.\n2. Prevent adjustments of font size after orientation changes in iOS.\n3. Use a more readable tab size.\n4. Use the user's configured `sans` font-family by default.\n5. Use the user's configured `sans` font-feature-settings by default.\n6. Use the user's configured `sans` font-variation-settings by default.\n7. Disable tap highlights on iOS\n*/\r\n\r\n\r\nhtml,\n:host {\n  line-height: 1.5; /* 1 */\n  -webkit-text-size-adjust: 100%; /* 2 */\n  -moz-tab-size: 4; /* 3 */\n  -o-tab-size: 4;\n     tab-size: 4; /* 3 */\n  font-family: var(--font-geist-sans), Inter, system-ui, sans-serif; /* 4 */\n  font-feature-settings: normal; /* 5 */\n  font-variation-settings: normal; /* 6 */\n  -webkit-tap-highlight-color: transparent; /* 7 */\n}\r\n\r\n\r\n/*\n1. Remove the margin in all browsers.\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\n*/\r\n\r\n\r\nbody {\n  margin: 0; /* 1 */\n  line-height: inherit; /* 2 */\n}\r\n\r\n\r\n/*\n1. Add the correct height in Firefox.\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\n3. Ensure horizontal rules are visible by default.\n*/\r\n\r\n\r\nhr {\n  height: 0; /* 1 */\n  color: inherit; /* 2 */\n  border-top-width: 1px; /* 3 */\n}\r\n\r\n\r\n/*\nAdd the correct text decoration in Chrome, Edge, and Safari.\n*/\r\n\r\n\r\nabbr:where([title]) {\n  -webkit-text-decoration: underline dotted;\n          text-decoration: underline dotted;\n}\r\n\r\n\r\n/*\nRemove the default font size and weight for headings.\n*/\r\n\r\n\r\nh1,\nh2,\nh3,\nh4,\nh5,\nh6 {\n  font-size: inherit;\n  font-weight: inherit;\n}\r\n\r\n\r\n/*\nReset links to optimize for opt-in styling instead of opt-out.\n*/\r\n\r\n\r\na {\n  color: inherit;\n  text-decoration: inherit;\n}\r\n\r\n\r\n/*\nAdd the correct font weight in Edge and Safari.\n*/\r\n\r\n\r\nb,\nstrong {\n  font-weight: bolder;\n}\r\n\r\n\r\n/*\n1. Use the user's configured `mono` font-family by default.\n2. Use the user's configured `mono` font-feature-settings by default.\n3. Use the user's configured `mono` font-variation-settings by default.\n4. Correct the odd `em` font sizing in all browsers.\n*/\r\n\r\n\r\ncode,\nkbd,\nsamp,\npre {\n  font-family: var(--font-geist-mono), Monaco, Consolas, monospace; /* 1 */\n  font-feature-settings: normal; /* 2 */\n  font-variation-settings: normal; /* 3 */\n  font-size: 1em; /* 4 */\n}\r\n\r\n\r\n/*\nAdd the correct font size in all browsers.\n*/\r\n\r\n\r\nsmall {\n  font-size: 80%;\n}\r\n\r\n\r\n/*\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\n*/\r\n\r\n\r\nsub,\nsup {\n  font-size: 75%;\n  line-height: 0;\n  position: relative;\n  vertical-align: baseline;\n}\r\n\r\n\r\nsub {\n  bottom: -0.25em;\n}\r\n\r\n\r\nsup {\n  top: -0.5em;\n}\r\n\r\n\r\n/*\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\n3. Remove gaps between table borders by default.\n*/\r\n\r\n\r\ntable {\n  text-indent: 0; /* 1 */\n  border-color: inherit; /* 2 */\n  border-collapse: collapse; /* 3 */\n}\r\n\r\n\r\n/*\n1. Change the font styles in all browsers.\n2. Remove the margin in Firefox and Safari.\n3. Remove default padding in all browsers.\n*/\r\n\r\n\r\nbutton,\ninput,\noptgroup,\nselect,\ntextarea {\n  font-family: inherit; /* 1 */\n  font-feature-settings: inherit; /* 1 */\n  font-variation-settings: inherit; /* 1 */\n  font-size: 100%; /* 1 */\n  font-weight: inherit; /* 1 */\n  line-height: inherit; /* 1 */\n  letter-spacing: inherit; /* 1 */\n  color: inherit; /* 1 */\n  margin: 0; /* 2 */\n  padding: 0; /* 3 */\n}\r\n\r\n\r\n/*\nRemove the inheritance of text transform in Edge and Firefox.\n*/\r\n\r\n\r\nbutton,\nselect {\n  text-transform: none;\n}\r\n\r\n\r\n/*\n1. Correct the inability to style clickable types in iOS and Safari.\n2. Remove default button styles.\n*/\r\n\r\n\r\nbutton,\ninput:where([type='button']),\ninput:where([type='reset']),\ninput:where([type='submit']) {\n  -webkit-appearance: button; /* 1 */\n  background-color: transparent; /* 2 */\n  background-image: none; /* 2 */\n}\r\n\r\n\r\n/*\nUse the modern Firefox focus style for all focusable elements.\n*/\r\n\r\n\r\n:-moz-focusring {\n  outline: auto;\n}\r\n\r\n\r\n/*\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\n*/\r\n\r\n\r\n:-moz-ui-invalid {\n  box-shadow: none;\n}\r\n\r\n\r\n/*\nAdd the correct vertical alignment in Chrome and Firefox.\n*/\r\n\r\n\r\nprogress {\n  vertical-align: baseline;\n}\r\n\r\n\r\n/*\nCorrect the cursor style of increment and decrement buttons in Safari.\n*/\r\n\r\n\r\n::-webkit-inner-spin-button,\n::-webkit-outer-spin-button {\n  height: auto;\n}\r\n\r\n\r\n/*\n1. Correct the odd appearance in Chrome and Safari.\n2. Correct the outline style in Safari.\n*/\r\n\r\n\r\n[type='search'] {\n  -webkit-appearance: textfield; /* 1 */\n  outline-offset: -2px; /* 2 */\n}\r\n\r\n\r\n/*\nRemove the inner padding in Chrome and Safari on macOS.\n*/\r\n\r\n\r\n::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\r\n\r\n\r\n/*\n1. Correct the inability to style clickable types in iOS and Safari.\n2. Change font properties to `inherit` in Safari.\n*/\r\n\r\n\r\n::-webkit-file-upload-button {\n  -webkit-appearance: button; /* 1 */\n  font: inherit; /* 2 */\n}\r\n\r\n\r\n/*\nAdd the correct display in Chrome and Safari.\n*/\r\n\r\n\r\nsummary {\n  display: list-item;\n}\r\n\r\n\r\n/*\nRemoves the default spacing and border for appropriate elements.\n*/\r\n\r\n\r\nblockquote,\ndl,\ndd,\nh1,\nh2,\nh3,\nh4,\nh5,\nh6,\nhr,\nfigure,\np,\npre {\n  margin: 0;\n}\r\n\r\n\r\nfieldset {\n  margin: 0;\n  padding: 0;\n}\r\n\r\n\r\nlegend {\n  padding: 0;\n}\r\n\r\n\r\nol,\nul,\nmenu {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n}\r\n\r\n\r\n/*\nReset default styling for dialogs.\n*/\r\n\r\n\r\ndialog {\n  padding: 0;\n}\r\n\r\n\r\n/*\nPrevent resizing textareas horizontally by default.\n*/\r\n\r\n\r\ntextarea {\n  resize: vertical;\n}\r\n\r\n\r\n/*\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\n2. Set the default placeholder color to the user's configured gray 400 color.\n*/\r\n\r\n\r\ninput::-moz-placeholder, textarea::-moz-placeholder {\n  opacity: 1; /* 1 */\n  color: #9ca3af; /* 2 */\n}\r\n\r\n\r\ninput::placeholder,\ntextarea::placeholder {\n  opacity: 1; /* 1 */\n  color: #9ca3af; /* 2 */\n}\r\n\r\n\r\n/*\nSet the default cursor for buttons.\n*/\r\n\r\n\r\nbutton,\n[role=\"button\"] {\n  cursor: pointer;\n}\r\n\r\n\r\n/*\nMake sure disabled buttons don't get the pointer cursor.\n*/\r\n\r\n\r\n:disabled {\n  cursor: default;\n}\r\n\r\n\r\n/*\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\n   This can trigger a poorly considered lint error in some tools but is included by design.\n*/\r\n\r\n\r\nimg,\nsvg,\nvideo,\ncanvas,\naudio,\niframe,\nembed,\nobject {\n  display: block; /* 1 */\n  vertical-align: middle; /* 2 */\n}\r\n\r\n\r\n/*\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\n*/\r\n\r\n\r\nimg,\nvideo {\n  max-width: 100%;\n  height: auto;\n}\r\n\r\n\r\n/* Make elements with the HTML hidden attribute stay hidden by default */\r\n\r\n\r\n[hidden]:where(:not([hidden=\"until-found\"])) {\n  display: none;\n}\r\n\r\n\r\n*{\r\n  border-color: hsl(var(--border));\r\n  outline-color: hsl(var(--ring) / 0.5);\r\n}\r\n\r\n\r\nbody{\r\n  background-color: hsl(var(--background));\r\n  font-family: var(--font-geist-sans), Inter, system-ui, sans-serif;\r\n  color: hsl(var(--foreground));\r\n    font-feature-settings: \"rlig\" 1, \"calt\" 1;\r\n    text-rendering: optimizeLegibility;\r\n    -webkit-font-smoothing: antialiased;\r\n    -moz-osx-font-smoothing: grayscale;\r\n}\r\n\r\n\r\n/* Optimización de carga de fuentes */\r\n\r\n\r\n@font-face {\r\n    font-family: 'Inter';\r\n    font-display: swap;\r\n  }\r\n.container{\r\n  width: 100%;\r\n  margin-right: auto;\r\n  margin-left: auto;\r\n  padding-right: 2rem;\r\n  padding-left: 2rem;\r\n}\r\n@media (min-width: 1400px){\r\n\r\n  .container{\r\n    max-width: 1400px;\r\n  }\r\n}\r\n/* Clases tipográficas semánticas */\r\n.text-display{\r\n  font-size: 2.25rem;\r\n  line-height: 1.3;\r\n  font-weight: 700;\r\n  letter-spacing: -0.02em;\r\n}\r\n.text-display-large{\r\n  font-size: 3rem;\r\n  line-height: 1.2;\r\n  font-weight: 700;\r\n  letter-spacing: -0.02em;\r\n}\r\n.text-heading{\r\n  font-size: 1.5rem;\r\n  line-height: 1.4;\r\n  font-weight: 600;\r\n  letter-spacing: -0.025em;\r\n}\r\n.text-heading-large{\r\n  font-size: 1.875rem;\r\n  line-height: 1.4;\r\n  font-weight: 600;\r\n  letter-spacing: -0.025em;\r\n}\r\n.text-subheading{\r\n  font-size: 1.25rem;\r\n  line-height: 1.5;\r\n  letter-spacing: -0.005em;\r\n  font-weight: 500;\r\n}\r\n.text-body{\r\n  font-size: 1rem;\r\n  line-height: 1.6;\r\n}\r\n.text-body-large{\r\n  font-size: 1.125rem;\r\n  line-height: 1.6;\r\n}\r\n.text-caption{\r\n  font-size: 0.875rem;\r\n  line-height: 1.25rem;\r\n  font-weight: 500;\r\n  color: hsl(var(--muted-foreground));\r\n}\r\n.text-caption-large{\r\n  font-size: 0.875rem;\r\n  line-height: 1.4;\r\n  letter-spacing: 0.01em;\r\n  font-weight: 500;\r\n  color: hsl(var(--muted-foreground));\r\n}\r\n.text-overline{\r\n  font-size: 0.6875rem;\r\n  line-height: 1.3;\r\n  font-weight: 600;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.05em;\r\n  color: hsl(var(--muted-foreground));\r\n}\r\n.text-code{\r\n  border-radius: 0.25rem;\r\n  background-color: hsl(var(--muted));\r\n  padding-left: 0.5rem;\r\n  padding-right: 0.5rem;\r\n  padding-top: 0.25rem;\r\n  padding-bottom: 0.25rem;\r\n  font-family: var(--font-geist-mono), Monaco, Consolas, monospace;\r\n  font-size: 0.875rem;\r\n  line-height: 1.5;\r\n  color: hsl(var(--foreground));\r\n}\r\n.text-code-inline{\r\n  border-radius: 0.25rem;\r\n  background-color: hsl(var(--muted));\r\n  padding-left: 0.375rem;\r\n  padding-right: 0.375rem;\r\n  padding-top: 0.125rem;\r\n  padding-bottom: 0.125rem;\r\n  font-family: var(--font-geist-mono), Monaco, Consolas, monospace;\r\n  font-size: 0.875rem;\r\n  line-height: 1.4;\r\n  letter-spacing: 0.01em;\r\n  color: hsl(var(--foreground));\r\n}\r\n/* Clases para números y métricas */\r\n.text-metric{\r\n  font-size: 3rem;\r\n  line-height: 1.2;\r\n  font-weight: 700;\r\n  --tw-numeric-spacing: tabular-nums;\r\n  font-variant-numeric: var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction);\r\n  letter-spacing: -0.025em;\r\n}\r\n.text-metric-large{\r\n  font-size: 3.75rem;\r\n  line-height: 1.2;\r\n  font-weight: 700;\r\n  --tw-numeric-spacing: tabular-nums;\r\n  font-variant-numeric: var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction);\r\n  letter-spacing: -0.025em;\r\n}\r\n.text-metric-small{\r\n  font-size: 1.5rem;\r\n  line-height: 1.4;\r\n  font-weight: 700;\r\n  --tw-numeric-spacing: tabular-nums;\r\n  font-variant-numeric: var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction);\r\n  letter-spacing: -0.025em;\r\n}\r\n/* Clases para contenido de texto largo */\r\n.text-prose{\r\n  font-size: 1rem;\r\n  max-width: 65ch;\r\n  line-height: 1.75;\r\n}\r\n.text-prose h1{\r\n  font-size: 3rem;\r\n  line-height: 1.2;\r\n  font-weight: 700;\r\n  letter-spacing: -0.02em;\r\n  margin-bottom: 1.5rem;\r\n  margin-top: 2rem;\r\n}\r\n.text-prose h1:first-child{\r\n  margin-top: 0px;\r\n}\r\n.text-prose h2{\r\n  font-size: 1.875rem;\r\n  line-height: 1.4;\r\n  font-weight: 600;\r\n  letter-spacing: -0.025em;\r\n  margin-bottom: 1rem;\r\n  margin-top: 1.5rem;\r\n}\r\n.text-prose h3{\r\n  font-size: 1.5rem;\r\n  line-height: 1.4;\r\n  font-weight: 600;\r\n  letter-spacing: -0.025em;\r\n  margin-bottom: 0.75rem;\r\n  margin-top: 1.25rem;\r\n}\r\n.text-prose h4{\r\n  font-size: 1.25rem;\r\n  line-height: 1.5;\r\n  letter-spacing: -0.005em;\r\n  font-weight: 500;\r\n  margin-bottom: 0.5rem;\r\n  margin-top: 1rem;\r\n}\r\n.text-prose p{\r\n  margin-bottom: 1rem;\r\n}\r\n.text-prose ul, .text-prose ol{\r\n  margin-bottom: 1rem;\r\n  padding-left: 1.5rem;\r\n}\r\n.text-prose li{\r\n  margin-bottom: 0.25rem;\r\n}\r\n/* Estados de interacción mejorados */\r\n.text-link{\r\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\r\n  transition-duration: 200ms;\r\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\r\n  animation-duration: 200ms;\r\n  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\r\n  color: hsl(var(--primary));\r\n  text-underline-offset: 4px;\r\n}\r\n.text-link:hover{\r\n  color: hsl(var(--primary) / 0.8);\r\n  text-decoration-line: underline;\r\n}\r\n/* Clases para toque creativo y exploración */\r\n.rayuela-accent{\r\n  color: hsl(var(--primary));\r\n}\r\n/* Gradientes sutiles para profundidad */\r\n.rayuela-card-gradient{\r\n  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));\r\n  --tw-gradient-from: hsl(var(--background)) var(--tw-gradient-from-position);\r\n  --tw-gradient-to: hsl(var(--background) / 0) var(--tw-gradient-to-position);\r\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\r\n  --tw-gradient-to: hsl(var(--muted) / 0.3) var(--tw-gradient-to-position);\r\n}\r\n.rayuela-subtle-gradient{\r\n  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));\r\n  --tw-gradient-from: hsl(var(--card)) var(--tw-gradient-from-position);\r\n  --tw-gradient-to: hsl(var(--card) / 0) var(--tw-gradient-to-position);\r\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\r\n  --tw-gradient-to: hsl(var(--card) / 0.9) var(--tw-gradient-to-position);\r\n}\r\n/* === UTILIDADES DE INTERACCIÓN Y ANIMACIÓN RAYUELA === */\r\n/* Base para todas las transiciones interactivas */\r\n.rayuela-interactive{\r\n  transition-property: all;\r\n  transition-duration: 200ms;\r\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\r\n  animation-duration: 200ms;\r\n  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\r\n}\r\n/* Efecto de elevación sutil al hacer hover */\r\n/* Efecto de \"presión\" al hacer click */\r\n.rayuela-active-press{\r\n  transition-property: transform;\r\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\r\n  transition-duration: 75ms;\r\n  animation-duration: 75ms;\r\n}\r\n.rayuela-active-press:active{\r\n  --tw-scale-x: .95;\r\n  --tw-scale-y: .95;\r\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\r\n}\r\n/* Sombras para hover en botones y tarjetas */\r\n.rayuela-shadow-hover{\r\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\r\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\r\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\r\n}\r\n.rayuela-shadow-hover:hover{\r\n  --tw-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);\r\n  --tw-shadow-colored: 0 2px 15px -3px var(--tw-shadow-color), 0 10px 20px -2px var(--tw-shadow-color);\r\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\r\n    box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);\r\n}\r\n/* Efecto de escala sutil en hover */\r\n.rayuela-scale-hover:hover{\r\n  --tw-scale-x: 1.02;\r\n  --tw-scale-y: 1.02;\r\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\r\n}\r\n/* Combinaciones específicas para componentes */\r\n.rayuela-card-hover{\r\n  transition-property: all;\r\n  transition-duration: 200ms;\r\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\r\n  animation-duration: 200ms;\r\n  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\r\n}\r\n@media (prefers-reduced-motion: reduce) {\r\n    .rayuela-card-hover {\r\n      animation: none;\r\n      transition: none;\r\n      transform: none;\r\n    }\r\n  }\r\n.rayuela-card-hover:hover{\r\n  --tw-translate-y: -0.125rem;\r\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\r\n}\r\n.rayuela-card-hover{\r\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\r\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\r\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\r\n}\r\n.rayuela-card-hover:hover{\r\n  --tw-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);\r\n  --tw-shadow-colored: 0 2px 15px -3px var(--tw-shadow-color), 0 10px 20px -2px var(--tw-shadow-color);\r\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\r\n    box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);\r\n}\r\n@media (prefers-reduced-motion: reduce) {\r\n    .rayuela-card-hover {\r\n      animation: none;\r\n      transition: none;\r\n      transform: none;\r\n    }\r\n  }\r\n/* === ANIMACIONES DE ENTRADA === */\r\n/* Animaciones de entrada para elementos que aparecen */\r\n.rayuela-fade-in {\r\n    animation: fade-in 0.6s ease-out;\r\n  }\r\n.rayuela-scale-in {\r\n    animation: scale-in 0.4s ease-out;\r\n  }\r\n.rayuela-slide-up {\r\n    animation: slide-up 0.5s ease-out;\r\n  }\r\n/* Utilidades para animaciones staggered */\r\n.rayuela-stagger-1 {\r\n    animation-delay: 0.1s;\r\n  }\r\n.rayuela-stagger-2 {\r\n    animation-delay: 0.2s;\r\n  }\r\n.rayuela-stagger-3 {\r\n    animation-delay: 0.3s;\r\n  }\r\n/* === OTRAS UTILIDADES DE ESTILO RAYUELA === */\r\n/* Gradient text effect */\r\n/* Enhanced shadows */\r\n/* Pulse animation for CTAs */\r\n/* === RESPETO A PREFERENCIAS DE MOVIMIENTO === */\r\n@media (prefers-reduced-motion: reduce) {\r\n    .rayuela-fade-in, .rayuela-scale-in, .rayuela-slide-up,\r\n    .rayuela-interactive, .rayuela-hover-lift, .rayuela-active-press, .rayuela-scale-hover,\r\n    .rayuela-pulse-cta {\r\n      animation: none !important;\r\n      transition: none !important;\r\n      transform: none !important;\r\n    }\r\n    .animate-shimmer, .rayuela-shimmer {\r\n      animation: none !important;\r\n      background: linear-gradient(90deg, transparent 0%, var(--muted) 50%, transparent 100%) !important;\r\n    }\r\n  }\r\n/* Efectos de progreso y exploración */\r\n.rayuela-progress-glow{\r\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\r\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\r\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\r\n}\r\n.rayuela-exploration-glow{\r\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\r\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\r\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\r\n}\r\n/* Estilos específicos para iconografía moderna */\r\n.rayuela-icon-accent{\r\n  color: hsl(var(--primary));\r\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\r\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\r\n  transition-duration: 150ms;\r\n}\r\n.group:hover .rayuela-icon-accent{\r\n  color: hsl(var(--primary) / 0.8);\r\n}\r\n.rayuela-icon-progress{\r\n  color: hsl(var(--success));\r\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\r\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\r\n  transition-duration: 150ms;\r\n}\r\n.group:hover .rayuela-icon-progress{\r\n  color: hsl(var(--success) / 0.8);\r\n}\r\n.rayuela-icon-exploration{\r\n  color: hsl(var(--info));\r\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\r\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\r\n  transition-duration: 150ms;\r\n}\r\n.group:hover .rayuela-icon-exploration{\r\n  color: hsl(var(--info) / 0.8);\r\n}\r\n/*\r\n  SISTEMA DE COLORES UNIFICADO - RAYUELA\r\n  =====================================\r\n  \r\n  Este sistema utiliza variables CSS basadas en oklch para garantizar:\r\n  - Consistencia entre modo claro y oscuro\r\n  - Armonía entre secciones de marketing y producto  \r\n  - Accesibilidad y contraste óptimo\r\n  \r\n  MEJORES PRÁCTICAS:\r\n  ✅ Usar: bg-background, bg-card, bg-muted\r\n  ❌ Evitar: bg-gray-100, bg-white, bg-blue-50\r\n  \r\n  ✅ Usar: text-foreground, text-muted-foreground  \r\n  ❌ Evitar: text-gray-800, text-blue-600\r\n  \r\n  ✅ Usar: Badge variant=\"success|warning|info\"\r\n  ❌ Evitar: bg-green-100 text-green-800\r\n  \r\n  ✅ Usar: from-marketing-gradient-start to-marketing-gradient-end\r\n  ❌ Evitar: from-blue-50 to-indigo-100\r\n  \r\n  Las variables oklch proporcionan transiciones suaves y \r\n  control preciso de luminancia para máxima accesibilidad.\r\n  */\r\n.btn-primary{\r\n  display: inline-flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border-radius: var(--radius);\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(147 51 234 / var(--tw-bg-opacity, 1));\r\n  padding-left: 1.25rem;\r\n  padding-right: 1.25rem;\r\n  padding-top: 0.625rem;\r\n  padding-bottom: 0.625rem;\r\n  font-weight: 500;\r\n  --tw-text-opacity: 1;\r\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\r\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;\r\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\r\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;\r\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\r\n  transition-duration: 150ms;\r\n}\r\n.btn-primary:hover{\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(126 34 206 / var(--tw-bg-opacity, 1));\r\n}\r\n.btn-secondary{\r\n  display: inline-flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border-radius: var(--radius);\r\n  border-width: 1px;\r\n  --tw-border-opacity: 1;\r\n  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\r\n  padding-left: 1.25rem;\r\n  padding-right: 1.25rem;\r\n  padding-top: 0.625rem;\r\n  padding-bottom: 0.625rem;\r\n  font-weight: 500;\r\n  --tw-text-opacity: 1;\r\n  color: rgb(55 65 81 / var(--tw-text-opacity, 1));\r\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;\r\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\r\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;\r\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\r\n  transition-duration: 150ms;\r\n}\r\n.btn-secondary:hover{\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\r\n}\r\n.sr-only{\r\n  position: absolute;\r\n  width: 1px;\r\n  height: 1px;\r\n  padding: 0;\r\n  margin: -1px;\r\n  overflow: hidden;\r\n  clip: rect(0, 0, 0, 0);\r\n  white-space: nowrap;\r\n  border-width: 0;\r\n}\r\n.visible{\r\n  visibility: visible;\r\n}\r\n.invisible{\r\n  visibility: hidden;\r\n}\r\n.static{\r\n  position: static;\r\n}\r\n.fixed{\r\n  position: fixed;\r\n}\r\n.absolute{\r\n  position: absolute;\r\n}\r\n.relative{\r\n  position: relative;\r\n}\r\n.sticky{\r\n  position: sticky;\r\n}\r\n.inset-0{\r\n  inset: 0px;\r\n}\r\n.-top-3{\r\n  top: -0.75rem;\r\n}\r\n.-top-4{\r\n  top: -1rem;\r\n}\r\n.-top-40{\r\n  top: -10rem;\r\n}\r\n.bottom-6{\r\n  bottom: 1.5rem;\r\n}\r\n.left-0{\r\n  left: 0px;\r\n}\r\n.left-1{\r\n  left: 0.25rem;\r\n}\r\n.left-1\\/2{\r\n  left: 50%;\r\n}\r\n.left-2{\r\n  left: 0.5rem;\r\n}\r\n.left-3{\r\n  left: 0.75rem;\r\n}\r\n.left-6{\r\n  left: 1.5rem;\r\n}\r\n.left-\\[50\\%\\]{\r\n  left: 50%;\r\n}\r\n.right-0{\r\n  right: 0px;\r\n}\r\n.right-1{\r\n  right: 0.25rem;\r\n}\r\n.right-2{\r\n  right: 0.5rem;\r\n}\r\n.right-3{\r\n  right: 0.75rem;\r\n}\r\n.right-4{\r\n  right: 1rem;\r\n}\r\n.right-6{\r\n  right: 1.5rem;\r\n}\r\n.top-0{\r\n  top: 0px;\r\n}\r\n.top-1\\/2{\r\n  top: 50%;\r\n}\r\n.top-16{\r\n  top: 4rem;\r\n}\r\n.top-2{\r\n  top: 0.5rem;\r\n}\r\n.top-3{\r\n  top: 0.75rem;\r\n}\r\n.top-4{\r\n  top: 1rem;\r\n}\r\n.top-8{\r\n  top: 2rem;\r\n}\r\n.top-\\[50\\%\\]{\r\n  top: 50%;\r\n}\r\n.z-10{\r\n  z-index: 10;\r\n}\r\n.z-30{\r\n  z-index: 30;\r\n}\r\n.z-40{\r\n  z-index: 40;\r\n}\r\n.z-50{\r\n  z-index: 50;\r\n}\r\n.z-\\[9999\\]{\r\n  z-index: 9999;\r\n}\r\n.order-1{\r\n  order: 1;\r\n}\r\n.order-2{\r\n  order: 2;\r\n}\r\n.col-span-1{\r\n  grid-column: span 1 / span 1;\r\n}\r\n.col-span-2{\r\n  grid-column: span 2 / span 2;\r\n}\r\n.col-start-2{\r\n  grid-column-start: 2;\r\n}\r\n.row-span-2{\r\n  grid-row: span 2 / span 2;\r\n}\r\n.row-start-1{\r\n  grid-row-start: 1;\r\n}\r\n.-m-1{\r\n  margin: -0.25rem;\r\n}\r\n.-mx-1{\r\n  margin-left: -0.25rem;\r\n  margin-right: -0.25rem;\r\n}\r\n.-mx-2{\r\n  margin-left: -0.5rem;\r\n  margin-right: -0.5rem;\r\n}\r\n.mx-2{\r\n  margin-left: 0.5rem;\r\n  margin-right: 0.5rem;\r\n}\r\n.mx-auto{\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n}\r\n.my-1{\r\n  margin-top: 0.25rem;\r\n  margin-bottom: 0.25rem;\r\n}\r\n.my-3{\r\n  margin-top: 0.75rem;\r\n  margin-bottom: 0.75rem;\r\n}\r\n.my-4{\r\n  margin-top: 1rem;\r\n  margin-bottom: 1rem;\r\n}\r\n.my-6{\r\n  margin-top: 1.5rem;\r\n  margin-bottom: 1.5rem;\r\n}\r\n.my-8{\r\n  margin-top: 2rem;\r\n  margin-bottom: 2rem;\r\n}\r\n.-mr-2{\r\n  margin-right: -0.5rem;\r\n}\r\n.mb-1{\r\n  margin-bottom: 0.25rem;\r\n}\r\n.mb-10{\r\n  margin-bottom: 2.5rem;\r\n}\r\n.mb-12{\r\n  margin-bottom: 3rem;\r\n}\r\n.mb-16{\r\n  margin-bottom: 4rem;\r\n}\r\n.mb-2{\r\n  margin-bottom: 0.5rem;\r\n}\r\n.mb-3{\r\n  margin-bottom: 0.75rem;\r\n}\r\n.mb-4{\r\n  margin-bottom: 1rem;\r\n}\r\n.mb-5{\r\n  margin-bottom: 1.25rem;\r\n}\r\n.mb-6{\r\n  margin-bottom: 1.5rem;\r\n}\r\n.mb-8{\r\n  margin-bottom: 2rem;\r\n}\r\n.ml-1{\r\n  margin-left: 0.25rem;\r\n}\r\n.ml-2{\r\n  margin-left: 0.5rem;\r\n}\r\n.ml-3{\r\n  margin-left: 0.75rem;\r\n}\r\n.ml-4{\r\n  margin-left: 1rem;\r\n}\r\n.ml-6{\r\n  margin-left: 1.5rem;\r\n}\r\n.ml-auto{\r\n  margin-left: auto;\r\n}\r\n.mr-1{\r\n  margin-right: 0.25rem;\r\n}\r\n.mr-1\\.5{\r\n  margin-right: 0.375rem;\r\n}\r\n.mr-2{\r\n  margin-right: 0.5rem;\r\n}\r\n.mr-3{\r\n  margin-right: 0.75rem;\r\n}\r\n.mr-4{\r\n  margin-right: 1rem;\r\n}\r\n.mt-0{\r\n  margin-top: 0px;\r\n}\r\n.mt-0\\.5{\r\n  margin-top: 0.125rem;\r\n}\r\n.mt-1{\r\n  margin-top: 0.25rem;\r\n}\r\n.mt-12{\r\n  margin-top: 3rem;\r\n}\r\n.mt-16{\r\n  margin-top: 4rem;\r\n}\r\n.mt-2{\r\n  margin-top: 0.5rem;\r\n}\r\n.mt-24{\r\n  margin-top: 6rem;\r\n}\r\n.mt-3{\r\n  margin-top: 0.75rem;\r\n}\r\n.mt-4{\r\n  margin-top: 1rem;\r\n}\r\n.mt-6{\r\n  margin-top: 1.5rem;\r\n}\r\n.mt-8{\r\n  margin-top: 2rem;\r\n}\r\n.block{\r\n  display: block;\r\n}\r\n.inline{\r\n  display: inline;\r\n}\r\n.flex{\r\n  display: flex;\r\n}\r\n.inline-flex{\r\n  display: inline-flex;\r\n}\r\n.table{\r\n  display: table;\r\n}\r\n.grid{\r\n  display: grid;\r\n}\r\n.hidden{\r\n  display: none;\r\n}\r\n.size-2\\.5{\r\n  width: 0.625rem;\r\n  height: 0.625rem;\r\n}\r\n.size-4{\r\n  width: 1rem;\r\n  height: 1rem;\r\n}\r\n.size-7{\r\n  width: 1.75rem;\r\n  height: 1.75rem;\r\n}\r\n.size-8{\r\n  width: 2rem;\r\n  height: 2rem;\r\n}\r\n.h-1{\r\n  height: 0.25rem;\r\n}\r\n.h-1\\.5{\r\n  height: 0.375rem;\r\n}\r\n.h-10{\r\n  height: 2.5rem;\r\n}\r\n.h-11{\r\n  height: 2.75rem;\r\n}\r\n.h-12{\r\n  height: 3rem;\r\n}\r\n.h-16{\r\n  height: 4rem;\r\n}\r\n.h-2{\r\n  height: 0.5rem;\r\n}\r\n.h-2\\.5{\r\n  height: 0.625rem;\r\n}\r\n.h-20{\r\n  height: 5rem;\r\n}\r\n.h-24{\r\n  height: 6rem;\r\n}\r\n.h-3{\r\n  height: 0.75rem;\r\n}\r\n.h-3\\.5{\r\n  height: 0.875rem;\r\n}\r\n.h-4{\r\n  height: 1rem;\r\n}\r\n.h-40{\r\n  height: 10rem;\r\n}\r\n.h-5{\r\n  height: 1.25rem;\r\n}\r\n.h-6{\r\n  height: 1.5rem;\r\n}\r\n.h-64{\r\n  height: 16rem;\r\n}\r\n.h-8{\r\n  height: 2rem;\r\n}\r\n.h-80{\r\n  height: 20rem;\r\n}\r\n.h-9{\r\n  height: 2.25rem;\r\n}\r\n.h-96{\r\n  height: 24rem;\r\n}\r\n.h-\\[400px\\]{\r\n  height: 400px;\r\n}\r\n.h-\\[var\\(--radix-select-trigger-height\\)\\]{\r\n  height: var(--radix-select-trigger-height);\r\n}\r\n.h-auto{\r\n  height: auto;\r\n}\r\n.h-fit{\r\n  height: -moz-fit-content;\r\n  height: fit-content;\r\n}\r\n.h-full{\r\n  height: 100%;\r\n}\r\n.h-px{\r\n  height: 1px;\r\n}\r\n.h-screen{\r\n  height: 100vh;\r\n}\r\n.max-h-96{\r\n  max-height: 24rem;\r\n}\r\n.max-h-48{\r\n  max-height: 12rem;\r\n}\r\n.max-h-\\[90vh\\]{\r\n  max-height: 90vh;\r\n}\r\n.min-h-screen{\r\n  min-height: 100vh;\r\n}\r\n.w-1\\/2{\r\n  width: 50%;\r\n}\r\n.w-1\\/3{\r\n  width: 33.333333%;\r\n}\r\n.w-10{\r\n  width: 2.5rem;\r\n}\r\n.w-12{\r\n  width: 3rem;\r\n}\r\n.w-16{\r\n  width: 4rem;\r\n}\r\n.w-2{\r\n  width: 0.5rem;\r\n}\r\n.w-2\\/3{\r\n  width: 66.666667%;\r\n}\r\n.w-20{\r\n  width: 5rem;\r\n}\r\n.w-24{\r\n  width: 6rem;\r\n}\r\n.w-3{\r\n  width: 0.75rem;\r\n}\r\n.w-3\\.5{\r\n  width: 0.875rem;\r\n}\r\n.w-3\\/4{\r\n  width: 75%;\r\n}\r\n.w-32{\r\n  width: 8rem;\r\n}\r\n.w-4{\r\n  width: 1rem;\r\n}\r\n.w-40{\r\n  width: 10rem;\r\n}\r\n.w-48{\r\n  width: 12rem;\r\n}\r\n.w-5{\r\n  width: 1.25rem;\r\n}\r\n.w-5\\/6{\r\n  width: 83.333333%;\r\n}\r\n.w-6{\r\n  width: 1.5rem;\r\n}\r\n.w-64{\r\n  width: 16rem;\r\n}\r\n.w-72{\r\n  width: 18rem;\r\n}\r\n.w-8{\r\n  width: 2rem;\r\n}\r\n.w-96{\r\n  width: 24rem;\r\n}\r\n.w-\\[150px\\]{\r\n  width: 150px;\r\n}\r\n.w-\\[200px\\]{\r\n  width: 200px;\r\n}\r\n.w-auto{\r\n  width: auto;\r\n}\r\n.w-fit{\r\n  width: -moz-fit-content;\r\n  width: fit-content;\r\n}\r\n.w-full{\r\n  width: 100%;\r\n}\r\n.min-w-0{\r\n  min-width: 0px;\r\n}\r\n.min-w-\\[8rem\\]{\r\n  min-width: 8rem;\r\n}\r\n.min-w-\\[var\\(--radix-select-trigger-width\\)\\]{\r\n  min-width: var(--radix-select-trigger-width);\r\n}\r\n.max-w-2xl{\r\n  max-width: 42rem;\r\n}\r\n.max-w-32{\r\n  max-width: 8rem;\r\n}\r\n.max-w-3xl{\r\n  max-width: 48rem;\r\n}\r\n.max-w-4xl{\r\n  max-width: 56rem;\r\n}\r\n.max-w-5xl{\r\n  max-width: 64rem;\r\n}\r\n.max-w-6xl{\r\n  max-width: 72rem;\r\n}\r\n.max-w-7xl{\r\n  max-width: 80rem;\r\n}\r\n.max-w-\\[calc\\(100\\%-2rem\\)\\]{\r\n  max-width: calc(100% - 2rem);\r\n}\r\n.max-w-lg{\r\n  max-width: 32rem;\r\n}\r\n.max-w-md{\r\n  max-width: 28rem;\r\n}\r\n.max-w-none{\r\n  max-width: none;\r\n}\r\n.max-w-prose{\r\n  max-width: 65ch;\r\n}\r\n.max-w-xs{\r\n  max-width: 20rem;\r\n}\r\n.max-w-sm{\r\n  max-width: 24rem;\r\n}\r\n.flex-1{\r\n  flex: 1 1 0%;\r\n}\r\n.flex-shrink-0{\r\n  flex-shrink: 0;\r\n}\r\n.shrink-0{\r\n  flex-shrink: 0;\r\n}\r\n.flex-grow{\r\n  flex-grow: 1;\r\n}\r\n.grow{\r\n  flex-grow: 1;\r\n}\r\n.caption-bottom{\r\n  caption-side: bottom;\r\n}\r\n.border-collapse{\r\n  border-collapse: collapse;\r\n}\r\n.-translate-x-1\\/2{\r\n  --tw-translate-x: -50%;\r\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\r\n}\r\n.-translate-y-1\\/2{\r\n  --tw-translate-y: -50%;\r\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\r\n}\r\n.translate-x-\\[-50\\%\\]{\r\n  --tw-translate-x: -50%;\r\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\r\n}\r\n.translate-y-\\[-50\\%\\]{\r\n  --tw-translate-y: -50%;\r\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\r\n}\r\n.translate-y-\\[calc\\(-50\\%_-_2px\\)\\]{\r\n  --tw-translate-y: calc(-50% - 2px);\r\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\r\n}\r\n.rotate-45{\r\n  --tw-rotate: 45deg;\r\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\r\n}\r\n.scale-105{\r\n  --tw-scale-x: 1.05;\r\n  --tw-scale-y: 1.05;\r\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\r\n}\r\n.transform{\r\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\r\n}\r\n@keyframes pulse{\r\n\r\n  50%{\r\n    opacity: .5;\r\n  }\r\n}\r\n.animate-pulse{\r\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\r\n}\r\n@keyframes shimmer{\r\n\r\n  from{\r\n    background-position: 0% 0%;\r\n  }\r\n\r\n  to{\r\n    background-position: -200% 0%;\r\n  }\r\n}\r\n.animate-shimmer{\r\n  animation: shimmer 2s linear infinite;\r\n}\r\n@keyframes spin{\r\n\r\n  to{\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n.animate-spin{\r\n  animation: spin 1s linear infinite;\r\n}\r\n.cursor-default{\r\n  cursor: default;\r\n}\r\n.cursor-help{\r\n  cursor: help;\r\n}\r\n.cursor-pointer{\r\n  cursor: pointer;\r\n}\r\n.touch-none{\r\n  touch-action: none;\r\n}\r\n.select-none{\r\n  -webkit-user-select: none;\r\n     -moz-user-select: none;\r\n          user-select: none;\r\n}\r\n.list-inside{\r\n  list-style-position: inside;\r\n}\r\n.list-decimal{\r\n  list-style-type: decimal;\r\n}\r\n.list-disc{\r\n  list-style-type: disc;\r\n}\r\n.list-none{\r\n  list-style-type: none;\r\n}\r\n.auto-rows-min{\r\n  grid-auto-rows: min-content;\r\n}\r\n.grid-cols-1{\r\n  grid-template-columns: repeat(1, minmax(0, 1fr));\r\n}\r\n.grid-cols-2{\r\n  grid-template-columns: repeat(2, minmax(0, 1fr));\r\n}\r\n.grid-cols-3{\r\n  grid-template-columns: repeat(3, minmax(0, 1fr));\r\n}\r\n.grid-cols-4{\r\n  grid-template-columns: repeat(4, minmax(0, 1fr));\r\n}\r\n.grid-rows-\\[auto_auto\\]{\r\n  grid-template-rows: auto auto;\r\n}\r\n.flex-col{\r\n  flex-direction: column;\r\n}\r\n.flex-col-reverse{\r\n  flex-direction: column-reverse;\r\n}\r\n.flex-wrap{\r\n  flex-wrap: wrap;\r\n}\r\n.items-start{\r\n  align-items: flex-start;\r\n}\r\n.items-end{\r\n  align-items: flex-end;\r\n}\r\n.items-center{\r\n  align-items: center;\r\n}\r\n.items-baseline{\r\n  align-items: baseline;\r\n}\r\n.items-stretch{\r\n  align-items: stretch;\r\n}\r\n.justify-start{\r\n  justify-content: flex-start;\r\n}\r\n.justify-end{\r\n  justify-content: flex-end;\r\n}\r\n.justify-center{\r\n  justify-content: center;\r\n}\r\n.justify-between{\r\n  justify-content: space-between;\r\n}\r\n.justify-items-center{\r\n  justify-items: center;\r\n}\r\n.gap-1{\r\n  gap: 0.25rem;\r\n}\r\n.gap-1\\.5{\r\n  gap: 0.375rem;\r\n}\r\n.gap-12{\r\n  gap: 3rem;\r\n}\r\n.gap-2{\r\n  gap: 0.5rem;\r\n}\r\n.gap-3{\r\n  gap: 0.75rem;\r\n}\r\n.gap-4{\r\n  gap: 1rem;\r\n}\r\n.gap-6{\r\n  gap: 1.5rem;\r\n}\r\n.gap-8{\r\n  gap: 2rem;\r\n}\r\n.gap-x-4{\r\n  -moz-column-gap: 1rem;\r\n       column-gap: 1rem;\r\n}\r\n.gap-y-2{\r\n  row-gap: 0.5rem;\r\n}\r\n.space-x-1 > :not([hidden]) ~ :not([hidden]){\r\n  --tw-space-x-reverse: 0;\r\n  margin-right: calc(0.25rem * var(--tw-space-x-reverse));\r\n  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));\r\n}\r\n.space-x-2 > :not([hidden]) ~ :not([hidden]){\r\n  --tw-space-x-reverse: 0;\r\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\r\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\r\n}\r\n.space-x-3 > :not([hidden]) ~ :not([hidden]){\r\n  --tw-space-x-reverse: 0;\r\n  margin-right: calc(0.75rem * var(--tw-space-x-reverse));\r\n  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));\r\n}\r\n.space-x-4 > :not([hidden]) ~ :not([hidden]){\r\n  --tw-space-x-reverse: 0;\r\n  margin-right: calc(1rem * var(--tw-space-x-reverse));\r\n  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\r\n}\r\n.space-y-1 > :not([hidden]) ~ :not([hidden]){\r\n  --tw-space-y-reverse: 0;\r\n  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));\r\n  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));\r\n}\r\n.space-y-1\\.5 > :not([hidden]) ~ :not([hidden]){\r\n  --tw-space-y-reverse: 0;\r\n  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));\r\n  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));\r\n}\r\n.space-y-12 > :not([hidden]) ~ :not([hidden]){\r\n  --tw-space-y-reverse: 0;\r\n  margin-top: calc(3rem * calc(1 - var(--tw-space-y-reverse)));\r\n  margin-bottom: calc(3rem * var(--tw-space-y-reverse));\r\n}\r\n.space-y-2 > :not([hidden]) ~ :not([hidden]){\r\n  --tw-space-y-reverse: 0;\r\n  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));\r\n  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));\r\n}\r\n.space-y-3 > :not([hidden]) ~ :not([hidden]){\r\n  --tw-space-y-reverse: 0;\r\n  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));\r\n  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));\r\n}\r\n.space-y-4 > :not([hidden]) ~ :not([hidden]){\r\n  --tw-space-y-reverse: 0;\r\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\r\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\r\n}\r\n.space-y-5 > :not([hidden]) ~ :not([hidden]){\r\n  --tw-space-y-reverse: 0;\r\n  margin-top: calc(1.25rem * calc(1 - var(--tw-space-y-reverse)));\r\n  margin-bottom: calc(1.25rem * var(--tw-space-y-reverse));\r\n}\r\n.space-y-6 > :not([hidden]) ~ :not([hidden]){\r\n  --tw-space-y-reverse: 0;\r\n  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));\r\n  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));\r\n}\r\n.space-y-8 > :not([hidden]) ~ :not([hidden]){\r\n  --tw-space-y-reverse: 0;\r\n  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));\r\n  margin-bottom: calc(2rem * var(--tw-space-y-reverse));\r\n}\r\n.self-start{\r\n  align-self: flex-start;\r\n}\r\n.justify-self-end{\r\n  justify-self: end;\r\n}\r\n.overflow-auto{\r\n  overflow: auto;\r\n}\r\n.overflow-hidden{\r\n  overflow: hidden;\r\n}\r\n.overflow-x-auto{\r\n  overflow-x: auto;\r\n}\r\n.overflow-y-auto{\r\n  overflow-y: auto;\r\n}\r\n.overflow-x-hidden{\r\n  overflow-x: hidden;\r\n}\r\n.truncate{\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n.whitespace-nowrap{\r\n  white-space: nowrap;\r\n}\r\n.text-balance{\r\n  text-wrap: balance;\r\n}\r\n.rounded{\r\n  border-radius: 0.25rem;\r\n}\r\n.rounded-2xl{\r\n  border-radius: 1rem;\r\n}\r\n.rounded-\\[2px\\]{\r\n  border-radius: 2px;\r\n}\r\n.rounded-full{\r\n  border-radius: 9999px;\r\n}\r\n.rounded-lg{\r\n  border-radius: var(--radius);\r\n}\r\n.rounded-md{\r\n  border-radius: calc(var(--radius) - 2px);\r\n}\r\n.rounded-sm{\r\n  border-radius: calc(var(--radius) - 4px);\r\n}\r\n.rounded-xl{\r\n  border-radius: 0.75rem;\r\n}\r\n.rounded-r-lg{\r\n  border-top-right-radius: var(--radius);\r\n  border-bottom-right-radius: var(--radius);\r\n}\r\n.border{\r\n  border-width: 1px;\r\n}\r\n.border-2{\r\n  border-width: 2px;\r\n}\r\n.border-b{\r\n  border-bottom-width: 1px;\r\n}\r\n.border-b-2{\r\n  border-bottom-width: 2px;\r\n}\r\n.border-l-2{\r\n  border-left-width: 2px;\r\n}\r\n.border-l-4{\r\n  border-left-width: 4px;\r\n}\r\n.border-r{\r\n  border-right-width: 1px;\r\n}\r\n.border-t{\r\n  border-top-width: 1px;\r\n}\r\n.border-dashed{\r\n  border-style: dashed;\r\n}\r\n.border-accent{\r\n  border-color: hsl(var(--accent));\r\n}\r\n.border-amber-300{\r\n  --tw-border-opacity: 1;\r\n  border-color: rgb(252 211 77 / var(--tw-border-opacity, 1));\r\n}\r\n.border-blue-200{\r\n  --tw-border-opacity: 1;\r\n  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));\r\n}\r\n.border-blue-300{\r\n  --tw-border-opacity: 1;\r\n  border-color: rgb(147 197 253 / var(--tw-border-opacity, 1));\r\n}\r\n.border-blue-500{\r\n  --tw-border-opacity: 1;\r\n  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));\r\n}\r\n.border-border{\r\n  border-color: hsl(var(--border));\r\n}\r\n.border-border\\/20{\r\n  border-color: hsl(var(--border) / 0.2);\r\n}\r\n.border-border\\/30{\r\n  border-color: hsl(var(--border) / 0.3);\r\n}\r\n.border-border\\/50{\r\n  border-color: hsl(var(--border) / 0.5);\r\n}\r\n.border-destructive{\r\n  border-color: hsl(var(--destructive));\r\n}\r\n.border-destructive\\/20{\r\n  border-color: hsl(var(--destructive) / 0.2);\r\n}\r\n.border-destructive\\/30{\r\n  border-color: hsl(var(--destructive) / 0.3);\r\n}\r\n.border-destructive\\/50{\r\n  border-color: hsl(var(--destructive) / 0.5);\r\n}\r\n.border-gray-100{\r\n  --tw-border-opacity: 1;\r\n  border-color: rgb(243 244 246 / var(--tw-border-opacity, 1));\r\n}\r\n.border-gray-400{\r\n  --tw-border-opacity: 1;\r\n  border-color: rgb(156 163 175 / var(--tw-border-opacity, 1));\r\n}\r\n.border-green-200{\r\n  --tw-border-opacity: 1;\r\n  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));\r\n}\r\n.border-green-500{\r\n  --tw-border-opacity: 1;\r\n  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));\r\n}\r\n.border-green-600{\r\n  --tw-border-opacity: 1;\r\n  border-color: rgb(22 163 74 / var(--tw-border-opacity, 1));\r\n}\r\n.border-indigo-200{\r\n  --tw-border-opacity: 1;\r\n  border-color: rgb(199 210 254 / var(--tw-border-opacity, 1));\r\n}\r\n.border-indigo-400{\r\n  --tw-border-opacity: 1;\r\n  border-color: rgb(129 140 248 / var(--tw-border-opacity, 1));\r\n}\r\n.border-info{\r\n  border-color: hsl(var(--info));\r\n}\r\n.border-info\\/20{\r\n  border-color: hsl(var(--info) / 0.2);\r\n}\r\n.border-info\\/30{\r\n  border-color: hsl(var(--info) / 0.3);\r\n}\r\n.border-info\\/40{\r\n  border-color: hsl(var(--info) / 0.4);\r\n}\r\n.border-info\\/50{\r\n  border-color: hsl(var(--info) / 0.5);\r\n}\r\n.border-input{\r\n  border-color: hsl(var(--input));\r\n}\r\n.border-orange-200{\r\n  --tw-border-opacity: 1;\r\n  border-color: rgb(254 215 170 / var(--tw-border-opacity, 1));\r\n}\r\n.border-primary{\r\n  border-color: hsl(var(--primary));\r\n}\r\n.border-primary\\/20{\r\n  border-color: hsl(var(--primary) / 0.2);\r\n}\r\n.border-purple-200{\r\n  --tw-border-opacity: 1;\r\n  border-color: rgb(233 213 255 / var(--tw-border-opacity, 1));\r\n}\r\n.border-purple-600{\r\n  --tw-border-opacity: 1;\r\n  border-color: rgb(147 51 234 / var(--tw-border-opacity, 1));\r\n}\r\n.border-red-200{\r\n  --tw-border-opacity: 1;\r\n  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));\r\n}\r\n.border-red-500{\r\n  --tw-border-opacity: 1;\r\n  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));\r\n}\r\n.border-red-600{\r\n  --tw-border-opacity: 1;\r\n  border-color: rgb(220 38 38 / var(--tw-border-opacity, 1));\r\n}\r\n.border-red-700{\r\n  --tw-border-opacity: 1;\r\n  border-color: rgb(185 28 28 / var(--tw-border-opacity, 1));\r\n}\r\n.border-slate-700{\r\n  --tw-border-opacity: 1;\r\n  border-color: rgb(51 65 85 / var(--tw-border-opacity, 1));\r\n}\r\n.border-success{\r\n  border-color: hsl(var(--success));\r\n}\r\n.border-success\\/20{\r\n  border-color: hsl(var(--success) / 0.2);\r\n}\r\n.border-success\\/30{\r\n  border-color: hsl(var(--success) / 0.3);\r\n}\r\n.border-success\\/40{\r\n  border-color: hsl(var(--success) / 0.4);\r\n}\r\n.border-success\\/50{\r\n  border-color: hsl(var(--success) / 0.5);\r\n}\r\n.border-transparent{\r\n  border-color: transparent;\r\n}\r\n.border-warning{\r\n  border-color: hsl(var(--warning));\r\n}\r\n.border-warning\\/20{\r\n  border-color: hsl(var(--warning) / 0.2);\r\n}\r\n.border-warning\\/30{\r\n  border-color: hsl(var(--warning) / 0.3);\r\n}\r\n.border-warning\\/40{\r\n  border-color: hsl(var(--warning) / 0.4);\r\n}\r\n.border-warning\\/50{\r\n  border-color: hsl(var(--warning) / 0.5);\r\n}\r\n.border-yellow-200{\r\n  --tw-border-opacity: 1;\r\n  border-color: rgb(254 240 138 / var(--tw-border-opacity, 1));\r\n}\r\n.bg-accent{\r\n  background-color: hsl(var(--accent));\r\n}\r\n.bg-amber-100{\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(254 243 199 / var(--tw-bg-opacity, 1));\r\n}\r\n.bg-amber-500{\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(245 158 11 / var(--tw-bg-opacity, 1));\r\n}\r\n.bg-background{\r\n  background-color: hsl(var(--background));\r\n}\r\n.bg-background\\/80{\r\n  background-color: hsl(var(--background) / 0.8);\r\n}\r\n.bg-black\\/50{\r\n  background-color: rgb(0 0 0 / 0.5);\r\n}\r\n.bg-black\\/80{\r\n  background-color: rgb(0 0 0 / 0.8);\r\n}\r\n.bg-blue-100{\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));\r\n}\r\n.bg-blue-50{\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));\r\n}\r\n.bg-blue-500{\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));\r\n}\r\n.bg-blue-600{\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));\r\n}\r\n.bg-card{\r\n  background-color: hsl(var(--card));\r\n}\r\n.bg-card\\/30{\r\n  background-color: hsl(var(--card) / 0.3);\r\n}\r\n.bg-card\\/50{\r\n  background-color: hsl(var(--card) / 0.5);\r\n}\r\n.bg-card\\/95{\r\n  background-color: hsl(var(--card) / 0.95);\r\n}\r\n.bg-destructive{\r\n  background-color: hsl(var(--destructive));\r\n}\r\n.bg-destructive\\/15{\r\n  background-color: hsl(var(--destructive) / 0.15);\r\n}\r\n.bg-destructive\\/5{\r\n  background-color: hsl(var(--destructive) / 0.05);\r\n}\r\n.bg-gray-200{\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));\r\n}\r\n.bg-gray-400{\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(156 163 175 / var(--tw-bg-opacity, 1));\r\n}\r\n.bg-gray-700{\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\r\n}\r\n.bg-gray-900{\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));\r\n}\r\n.bg-green-100{\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));\r\n}\r\n.bg-green-200{\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(187 247 208 / var(--tw-bg-opacity, 1));\r\n}\r\n.bg-green-50{\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));\r\n}\r\n.bg-green-500{\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));\r\n}\r\n.bg-green-600{\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));\r\n}\r\n.bg-indigo-100{\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(224 231 255 / var(--tw-bg-opacity, 1));\r\n}\r\n.bg-indigo-200{\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(199 210 254 / var(--tw-bg-opacity, 1));\r\n}\r\n.bg-indigo-50{\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(238 242 255 / var(--tw-bg-opacity, 1));\r\n}\r\n.bg-info{\r\n  background-color: hsl(var(--info));\r\n}\r\n.bg-info-light{\r\n  background-color: hsl(var(--info-light));\r\n}\r\n.bg-info-light\\/50{\r\n  background-color: hsl(var(--info-light) / 0.5);\r\n}\r\n.bg-info\\/10{\r\n  background-color: hsl(var(--info) / 0.1);\r\n}\r\n.bg-info\\/15{\r\n  background-color: hsl(var(--info) / 0.15);\r\n}\r\n.bg-muted{\r\n  background-color: hsl(var(--muted));\r\n}\r\n.bg-muted\\/10{\r\n  background-color: hsl(var(--muted) / 0.1);\r\n}\r\n.bg-muted\\/20{\r\n  background-color: hsl(var(--muted) / 0.2);\r\n}\r\n.bg-muted\\/30{\r\n  background-color: hsl(var(--muted) / 0.3);\r\n}\r\n.bg-muted\\/5{\r\n  background-color: hsl(var(--muted) / 0.05);\r\n}\r\n.bg-muted\\/50{\r\n  background-color: hsl(var(--muted) / 0.5);\r\n}\r\n.bg-orange-100{\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(255 237 213 / var(--tw-bg-opacity, 1));\r\n}\r\n.bg-orange-50{\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(255 247 237 / var(--tw-bg-opacity, 1));\r\n}\r\n.bg-orange-600{\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(234 88 12 / var(--tw-bg-opacity, 1));\r\n}\r\n.bg-popover{\r\n  background-color: hsl(var(--popover));\r\n}\r\n.bg-primary{\r\n  background-color: hsl(var(--primary));\r\n}\r\n.bg-primary\\/10{\r\n  background-color: hsl(var(--primary) / 0.1);\r\n}\r\n.bg-purple-100{\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));\r\n}\r\n.bg-purple-50{\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(250 245 255 / var(--tw-bg-opacity, 1));\r\n}\r\n.bg-purple-50\\/50{\r\n  background-color: rgb(250 245 255 / 0.5);\r\n}\r\n.bg-purple-600{\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(147 51 234 / var(--tw-bg-opacity, 1));\r\n}\r\n.bg-red-100{\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));\r\n}\r\n.bg-red-200{\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(254 202 202 / var(--tw-bg-opacity, 1));\r\n}\r\n.bg-red-50{\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));\r\n}\r\n.bg-red-500{\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));\r\n}\r\n.bg-red-900\\/30{\r\n  background-color: rgb(127 29 29 / 0.3);\r\n}\r\n.bg-secondary{\r\n  background-color: hsl(var(--secondary));\r\n}\r\n.bg-slate-800{\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(30 41 59 / var(--tw-bg-opacity, 1));\r\n}\r\n.bg-success{\r\n  background-color: hsl(var(--success));\r\n}\r\n.bg-success-light{\r\n  background-color: hsl(var(--success-light));\r\n}\r\n.bg-success\\/10{\r\n  background-color: hsl(var(--success) / 0.1);\r\n}\r\n.bg-success\\/15{\r\n  background-color: hsl(var(--success) / 0.15);\r\n}\r\n.bg-teal-100{\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(204 251 241 / var(--tw-bg-opacity, 1));\r\n}\r\n.bg-transparent{\r\n  background-color: transparent;\r\n}\r\n.bg-warning{\r\n  background-color: hsl(var(--warning));\r\n}\r\n.bg-warning-light{\r\n  background-color: hsl(var(--warning-light));\r\n}\r\n.bg-warning-light\\/50{\r\n  background-color: hsl(var(--warning-light) / 0.5);\r\n}\r\n.bg-warning\\/10{\r\n  background-color: hsl(var(--warning) / 0.1);\r\n}\r\n.bg-warning\\/15{\r\n  background-color: hsl(var(--warning) / 0.15);\r\n}\r\n.bg-white{\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\r\n}\r\n.bg-yellow-100{\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1));\r\n}\r\n.bg-yellow-50{\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));\r\n}\r\n.bg-yellow-500{\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));\r\n}\r\n.bg-gradient-to-br{\r\n  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));\r\n}\r\n.bg-gradient-to-r{\r\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\r\n}\r\n.from-blue-400{\r\n  --tw-gradient-from: #60a5fa var(--tw-gradient-from-position);\r\n  --tw-gradient-to: rgb(96 165 250 / 0) var(--tw-gradient-to-position);\r\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\r\n}\r\n.from-blue-50{\r\n  --tw-gradient-from: #eff6ff var(--tw-gradient-from-position);\r\n  --tw-gradient-to: rgb(239 246 255 / 0) var(--tw-gradient-to-position);\r\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\r\n}\r\n.from-blue-500{\r\n  --tw-gradient-from: #3b82f6 var(--tw-gradient-from-position);\r\n  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);\r\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\r\n}\r\n.from-gradient-semantic-brand-start{\r\n  --tw-gradient-from: hsl(var(--gradient-brand-start)) var(--tw-gradient-from-position);\r\n  --tw-gradient-to: hsl(var(--gradient-brand-start) / 0) var(--tw-gradient-to-position);\r\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\r\n}\r\n.from-gradient-semantic-success-start{\r\n  --tw-gradient-from: hsl(var(--gradient-success-start)) var(--tw-gradient-from-position);\r\n  --tw-gradient-to: hsl(var(--gradient-success-start) / 0) var(--tw-gradient-to-position);\r\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\r\n}\r\n.from-gray-400{\r\n  --tw-gradient-from: #9ca3af var(--tw-gradient-from-position);\r\n  --tw-gradient-to: rgb(156 163 175 / 0) var(--tw-gradient-to-position);\r\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\r\n}\r\n.from-green-400{\r\n  --tw-gradient-from: #4ade80 var(--tw-gradient-from-position);\r\n  --tw-gradient-to: rgb(74 222 128 / 0) var(--tw-gradient-to-position);\r\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\r\n}\r\n.from-green-500{\r\n  --tw-gradient-from: #22c55e var(--tw-gradient-from-position);\r\n  --tw-gradient-to: rgb(34 197 94 / 0) var(--tw-gradient-to-position);\r\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\r\n}\r\n.from-indigo-50{\r\n  --tw-gradient-from: #eef2ff var(--tw-gradient-from-position);\r\n  --tw-gradient-to: rgb(238 242 255 / 0) var(--tw-gradient-to-position);\r\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\r\n}\r\n.from-marketing-gradient-start{\r\n  --tw-gradient-from: hsl(var(--marketing-gradient-start)) var(--tw-gradient-from-position);\r\n  --tw-gradient-to: hsl(var(--marketing-gradient-start) / 0) var(--tw-gradient-to-position);\r\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\r\n}\r\n.from-muted{\r\n  --tw-gradient-from: hsl(var(--muted)) var(--tw-gradient-from-position);\r\n  --tw-gradient-to: hsl(var(--muted) / 0) var(--tw-gradient-to-position);\r\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\r\n}\r\n.from-primary{\r\n  --tw-gradient-from: hsl(var(--primary)) var(--tw-gradient-from-position);\r\n  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);\r\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\r\n}\r\n.from-primary\\/10{\r\n  --tw-gradient-from: hsl(var(--primary) / 0.1) var(--tw-gradient-from-position);\r\n  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);\r\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\r\n}\r\n.from-primary\\/5{\r\n  --tw-gradient-from: hsl(var(--primary) / 0.05) var(--tw-gradient-from-position);\r\n  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);\r\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\r\n}\r\n.from-purple-400{\r\n  --tw-gradient-from: #c084fc var(--tw-gradient-from-position);\r\n  --tw-gradient-to: rgb(192 132 252 / 0) var(--tw-gradient-to-position);\r\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\r\n}\r\n.from-purple-600{\r\n  --tw-gradient-from: #9333ea var(--tw-gradient-from-position);\r\n  --tw-gradient-to: rgb(147 51 234 / 0) var(--tw-gradient-to-position);\r\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\r\n}\r\n.from-transparent{\r\n  --tw-gradient-from: transparent var(--tw-gradient-from-position);\r\n  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);\r\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\r\n}\r\n.from-white{\r\n  --tw-gradient-from: #fff var(--tw-gradient-from-position);\r\n  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);\r\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\r\n}\r\n.via-border\\/30{\r\n  --tw-gradient-to: hsl(var(--border) / 0)  var(--tw-gradient-to-position);\r\n  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--border) / 0.3) var(--tw-gradient-via-position), var(--tw-gradient-to);\r\n}\r\n.via-muted\\/50{\r\n  --tw-gradient-to: hsl(var(--muted) / 0)  var(--tw-gradient-to-position);\r\n  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--muted) / 0.5) var(--tw-gradient-via-position), var(--tw-gradient-to);\r\n}\r\n.via-sky-50{\r\n  --tw-gradient-to: rgb(240 249 255 / 0)  var(--tw-gradient-to-position);\r\n  --tw-gradient-stops: var(--tw-gradient-from), #f0f9ff var(--tw-gradient-via-position), var(--tw-gradient-to);\r\n}\r\n.to-accent{\r\n  --tw-gradient-to: hsl(var(--accent)) var(--tw-gradient-to-position);\r\n}\r\n.to-accent\\/10{\r\n  --tw-gradient-to: hsl(var(--accent) / 0.1) var(--tw-gradient-to-position);\r\n}\r\n.to-accent\\/5{\r\n  --tw-gradient-to: hsl(var(--accent) / 0.05) var(--tw-gradient-to-position);\r\n}\r\n.to-blue-600{\r\n  --tw-gradient-to: #2563eb var(--tw-gradient-to-position);\r\n}\r\n.to-emerald-500{\r\n  --tw-gradient-to: #10b981 var(--tw-gradient-to-position);\r\n}\r\n.to-gradient-semantic-brand-end{\r\n  --tw-gradient-to: hsl(var(--gradient-brand-end)) var(--tw-gradient-to-position);\r\n}\r\n.to-gradient-semantic-success-end{\r\n  --tw-gradient-to: hsl(var(--gradient-success-end)) var(--tw-gradient-to-position);\r\n}\r\n.to-gray-500{\r\n  --tw-gradient-to: #6b7280 var(--tw-gradient-to-position);\r\n}\r\n.to-green-600{\r\n  --tw-gradient-to: #16a34a var(--tw-gradient-to-position);\r\n}\r\n.to-indigo-100{\r\n  --tw-gradient-to: #e0e7ff var(--tw-gradient-to-position);\r\n}\r\n.to-indigo-50{\r\n  --tw-gradient-to: #eef2ff var(--tw-gradient-to-position);\r\n}\r\n.to-indigo-500{\r\n  --tw-gradient-to: #6366f1 var(--tw-gradient-to-position);\r\n}\r\n.to-marketing-gradient-end{\r\n  --tw-gradient-to: hsl(var(--marketing-gradient-end)) var(--tw-gradient-to-position);\r\n}\r\n.to-muted{\r\n  --tw-gradient-to: hsl(var(--muted)) var(--tw-gradient-to-position);\r\n}\r\n.to-purple-50{\r\n  --tw-gradient-to: #faf5ff var(--tw-gradient-to-position);\r\n}\r\n.to-purple-600{\r\n  --tw-gradient-to: #9333ea var(--tw-gradient-to-position);\r\n}\r\n.to-purple-700{\r\n  --tw-gradient-to: #7e22ce var(--tw-gradient-to-position);\r\n}\r\n.to-sky-100{\r\n  --tw-gradient-to: #e0f2fe var(--tw-gradient-to-position);\r\n}\r\n.to-transparent{\r\n  --tw-gradient-to: transparent var(--tw-gradient-to-position);\r\n}\r\n.bg-\\[length\\:200\\%_100\\%\\]{\r\n  background-size: 200% 100%;\r\n}\r\n.bg-clip-text{\r\n  -webkit-background-clip: text;\r\n          background-clip: text;\r\n}\r\n.fill-primary{\r\n  fill: hsl(var(--primary));\r\n}\r\n.fill-yellow-400{\r\n  fill: #facc15;\r\n}\r\n.object-contain{\r\n  -o-object-fit: contain;\r\n     object-fit: contain;\r\n}\r\n.p-0{\r\n  padding: 0px;\r\n}\r\n.p-1{\r\n  padding: 0.25rem;\r\n}\r\n.p-1\\.5{\r\n  padding: 0.375rem;\r\n}\r\n.p-2{\r\n  padding: 0.5rem;\r\n}\r\n.p-3{\r\n  padding: 0.75rem;\r\n}\r\n.p-4{\r\n  padding: 1rem;\r\n}\r\n.p-6{\r\n  padding: 1.5rem;\r\n}\r\n.p-8{\r\n  padding: 2rem;\r\n}\r\n.px-0{\r\n  padding-left: 0px;\r\n  padding-right: 0px;\r\n}\r\n.px-1{\r\n  padding-left: 0.25rem;\r\n  padding-right: 0.25rem;\r\n}\r\n.px-1\\.5{\r\n  padding-left: 0.375rem;\r\n  padding-right: 0.375rem;\r\n}\r\n.px-2{\r\n  padding-left: 0.5rem;\r\n  padding-right: 0.5rem;\r\n}\r\n.px-2\\.5{\r\n  padding-left: 0.625rem;\r\n  padding-right: 0.625rem;\r\n}\r\n.px-3{\r\n  padding-left: 0.75rem;\r\n  padding-right: 0.75rem;\r\n}\r\n.px-4{\r\n  padding-left: 1rem;\r\n  padding-right: 1rem;\r\n}\r\n.px-6{\r\n  padding-left: 1.5rem;\r\n  padding-right: 1.5rem;\r\n}\r\n.px-8{\r\n  padding-left: 2rem;\r\n  padding-right: 2rem;\r\n}\r\n.py-0{\r\n  padding-top: 0px;\r\n  padding-bottom: 0px;\r\n}\r\n.py-0\\.5{\r\n  padding-top: 0.125rem;\r\n  padding-bottom: 0.125rem;\r\n}\r\n.py-1{\r\n  padding-top: 0.25rem;\r\n  padding-bottom: 0.25rem;\r\n}\r\n.py-1\\.5{\r\n  padding-top: 0.375rem;\r\n  padding-bottom: 0.375rem;\r\n}\r\n.py-14{\r\n  padding-top: 3.5rem;\r\n  padding-bottom: 3.5rem;\r\n}\r\n.py-16{\r\n  padding-top: 4rem;\r\n  padding-bottom: 4rem;\r\n}\r\n.py-2{\r\n  padding-top: 0.5rem;\r\n  padding-bottom: 0.5rem;\r\n}\r\n.py-3{\r\n  padding-top: 0.75rem;\r\n  padding-bottom: 0.75rem;\r\n}\r\n.py-4{\r\n  padding-top: 1rem;\r\n  padding-bottom: 1rem;\r\n}\r\n.py-6{\r\n  padding-top: 1.5rem;\r\n  padding-bottom: 1.5rem;\r\n}\r\n.py-8{\r\n  padding-top: 2rem;\r\n  padding-bottom: 2rem;\r\n}\r\n.pb-2{\r\n  padding-bottom: 0.5rem;\r\n}\r\n.pb-3{\r\n  padding-bottom: 0.75rem;\r\n}\r\n.pb-4{\r\n  padding-bottom: 1rem;\r\n}\r\n.pb-6{\r\n  padding-bottom: 1.5rem;\r\n}\r\n.pb-8{\r\n  padding-bottom: 2rem;\r\n}\r\n.pl-10{\r\n  padding-left: 2.5rem;\r\n}\r\n.pl-2{\r\n  padding-left: 0.5rem;\r\n}\r\n.pl-3{\r\n  padding-left: 0.75rem;\r\n}\r\n.pl-4{\r\n  padding-left: 1rem;\r\n}\r\n.pl-6{\r\n  padding-left: 1.5rem;\r\n}\r\n.pl-8{\r\n  padding-left: 2rem;\r\n}\r\n.pr-2{\r\n  padding-right: 0.5rem;\r\n}\r\n.pt-0{\r\n  padding-top: 0px;\r\n}\r\n.pt-1{\r\n  padding-top: 0.25rem;\r\n}\r\n.pt-12{\r\n  padding-top: 3rem;\r\n}\r\n.pt-2{\r\n  padding-top: 0.5rem;\r\n}\r\n.pt-4{\r\n  padding-top: 1rem;\r\n}\r\n.pt-6{\r\n  padding-top: 1.5rem;\r\n}\r\n.pt-8{\r\n  padding-top: 2rem;\r\n}\r\n.text-left{\r\n  text-align: left;\r\n}\r\n.text-center{\r\n  text-align: center;\r\n}\r\n.text-right{\r\n  text-align: right;\r\n}\r\n.align-middle{\r\n  vertical-align: middle;\r\n}\r\n.font-mono{\r\n  font-family: var(--font-geist-mono), Monaco, Consolas, monospace;\r\n}\r\n.text-2xl{\r\n  font-size: 1.5rem;\r\n  line-height: 2rem;\r\n}\r\n.text-3xl{\r\n  font-size: 1.875rem;\r\n  line-height: 2.25rem;\r\n}\r\n.text-4xl{\r\n  font-size: 2.25rem;\r\n  line-height: 2.5rem;\r\n}\r\n.text-6xl{\r\n  font-size: 3.75rem;\r\n  line-height: 1;\r\n}\r\n.text-\\[0\\.8rem\\]{\r\n  font-size: 0.8rem;\r\n}\r\n.text-\\[1\\.05rem\\]{\r\n  font-size: 1.05rem;\r\n}\r\n.text-\\[10px\\]{\r\n  font-size: 10px;\r\n}\r\n.text-base{\r\n  font-size: 1rem;\r\n  line-height: 1.5rem;\r\n}\r\n.text-body-lg{\r\n  font-size: 1.125rem;\r\n  line-height: 1.6;\r\n}\r\n.text-body-sm{\r\n  font-size: 0.875rem;\r\n  line-height: 1.5;\r\n}\r\n.text-caption{\r\n  font-size: 0.75rem;\r\n  line-height: 1.4;\r\n  letter-spacing: 0.01em;\r\n}\r\n.text-caption-lg{\r\n  font-size: 0.875rem;\r\n  line-height: 1.4;\r\n  letter-spacing: 0.01em;\r\n}\r\n.text-display-lg{\r\n  font-size: 3rem;\r\n  line-height: 1.2;\r\n  letter-spacing: -0.01em;\r\n}\r\n.text-display-md{\r\n  font-size: 2.25rem;\r\n  line-height: 1.3;\r\n  letter-spacing: -0.01em;\r\n}\r\n.text-heading-lg{\r\n  font-size: 1.5rem;\r\n  line-height: 1.4;\r\n  letter-spacing: -0.005em;\r\n}\r\n.text-heading-md{\r\n  font-size: 1.25rem;\r\n  line-height: 1.5;\r\n  letter-spacing: -0.005em;\r\n}\r\n.text-lg{\r\n  font-size: 1.125rem;\r\n  line-height: 1.75rem;\r\n}\r\n.text-sm{\r\n  font-size: 0.875rem;\r\n  line-height: 1.25rem;\r\n}\r\n.text-xl{\r\n  font-size: 1.25rem;\r\n  line-height: 1.75rem;\r\n}\r\n.text-xs{\r\n  font-size: 0.75rem;\r\n  line-height: 1rem;\r\n}\r\n.font-bold{\r\n  font-weight: 700;\r\n}\r\n.font-extrabold{\r\n  font-weight: 800;\r\n}\r\n.font-light{\r\n  font-weight: 300;\r\n}\r\n.font-medium{\r\n  font-weight: 500;\r\n}\r\n.font-normal{\r\n  font-weight: 400;\r\n}\r\n.font-semibold{\r\n  font-weight: 600;\r\n}\r\n.uppercase{\r\n  text-transform: uppercase;\r\n}\r\n.capitalize{\r\n  text-transform: capitalize;\r\n}\r\n.italic{\r\n  font-style: italic;\r\n}\r\n.not-italic{\r\n  font-style: normal;\r\n}\r\n.tabular-nums{\r\n  --tw-numeric-spacing: tabular-nums;\r\n  font-variant-numeric: var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction);\r\n}\r\n.leading-extra-tight{\r\n  line-height: 1.1;\r\n}\r\n.leading-none{\r\n  line-height: 1;\r\n}\r\n.leading-relaxed{\r\n  line-height: 1.75;\r\n}\r\n.leading-tight{\r\n  line-height: 1.25;\r\n}\r\n.tracking-extra-tight{\r\n  letter-spacing: -0.02em;\r\n}\r\n.tracking-tight{\r\n  letter-spacing: -0.025em;\r\n}\r\n.tracking-wider{\r\n  letter-spacing: 0.05em;\r\n}\r\n.text-accent{\r\n  color: hsl(var(--accent));\r\n}\r\n.text-accent-foreground{\r\n  color: hsl(var(--accent-foreground));\r\n}\r\n.text-amber-500{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(245 158 11 / var(--tw-text-opacity, 1));\r\n}\r\n.text-amber-600{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(217 119 6 / var(--tw-text-opacity, 1));\r\n}\r\n.text-amber-700{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(180 83 9 / var(--tw-text-opacity, 1));\r\n}\r\n.text-blue-400{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(96 165 250 / var(--tw-text-opacity, 1));\r\n}\r\n.text-blue-500{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(59 130 246 / var(--tw-text-opacity, 1));\r\n}\r\n.text-blue-600{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(37 99 235 / var(--tw-text-opacity, 1));\r\n}\r\n.text-blue-700{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(29 78 216 / var(--tw-text-opacity, 1));\r\n}\r\n.text-blue-800{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(30 64 175 / var(--tw-text-opacity, 1));\r\n}\r\n.text-blue-900{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(30 58 138 / var(--tw-text-opacity, 1));\r\n}\r\n.text-card-foreground{\r\n  color: hsl(var(--card-foreground));\r\n}\r\n.text-current{\r\n  color: currentColor;\r\n}\r\n.text-destructive{\r\n  color: hsl(var(--destructive));\r\n}\r\n.text-destructive-foreground{\r\n  color: hsl(var(--destructive-foreground));\r\n}\r\n.text-destructive\\/70{\r\n  color: hsl(var(--destructive) / 0.7);\r\n}\r\n.text-emerald-400{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(52 211 153 / var(--tw-text-opacity, 1));\r\n}\r\n.text-foreground{\r\n  color: hsl(var(--foreground));\r\n}\r\n.text-gray-100{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(243 244 246 / var(--tw-text-opacity, 1));\r\n}\r\n.text-gray-200{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(229 231 235 / var(--tw-text-opacity, 1));\r\n}\r\n.text-green-300{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(134 239 172 / var(--tw-text-opacity, 1));\r\n}\r\n.text-green-400{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(74 222 128 / var(--tw-text-opacity, 1));\r\n}\r\n.text-green-500{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(34 197 94 / var(--tw-text-opacity, 1));\r\n}\r\n.text-green-600{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(22 163 74 / var(--tw-text-opacity, 1));\r\n}\r\n.text-green-700{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(21 128 61 / var(--tw-text-opacity, 1));\r\n}\r\n.text-green-800{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(22 101 52 / var(--tw-text-opacity, 1));\r\n}\r\n.text-indigo-500{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(99 102 241 / var(--tw-text-opacity, 1));\r\n}\r\n.text-indigo-600{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(79 70 229 / var(--tw-text-opacity, 1));\r\n}\r\n.text-info{\r\n  color: hsl(var(--info));\r\n}\r\n.text-info-foreground{\r\n  color: hsl(var(--info-foreground));\r\n}\r\n.text-info\\/70{\r\n  color: hsl(var(--info) / 0.7);\r\n}\r\n.text-muted-foreground{\r\n  color: hsl(var(--muted-foreground));\r\n}\r\n.text-muted-foreground\\/50{\r\n  color: hsl(var(--muted-foreground) / 0.5);\r\n}\r\n.text-muted-foreground\\/70{\r\n  color: hsl(var(--muted-foreground) / 0.7);\r\n}\r\n.text-orange-500{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(249 115 22 / var(--tw-text-opacity, 1));\r\n}\r\n.text-orange-600{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(234 88 12 / var(--tw-text-opacity, 1));\r\n}\r\n.text-orange-700{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(194 65 12 / var(--tw-text-opacity, 1));\r\n}\r\n.text-popover-foreground{\r\n  color: hsl(var(--popover-foreground));\r\n}\r\n.text-primary{\r\n  color: hsl(var(--primary));\r\n}\r\n.text-primary-foreground{\r\n  color: hsl(var(--primary-foreground));\r\n}\r\n.text-purple-500{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(168 85 247 / var(--tw-text-opacity, 1));\r\n}\r\n.text-purple-600{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(147 51 234 / var(--tw-text-opacity, 1));\r\n}\r\n.text-purple-700{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(126 34 206 / var(--tw-text-opacity, 1));\r\n}\r\n.text-red-300{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(252 165 165 / var(--tw-text-opacity, 1));\r\n}\r\n.text-red-400{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(248 113 113 / var(--tw-text-opacity, 1));\r\n}\r\n.text-red-500{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(239 68 68 / var(--tw-text-opacity, 1));\r\n}\r\n.text-red-600{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(220 38 38 / var(--tw-text-opacity, 1));\r\n}\r\n.text-red-700{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(185 28 28 / var(--tw-text-opacity, 1));\r\n}\r\n.text-red-800{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(153 27 27 / var(--tw-text-opacity, 1));\r\n}\r\n.text-secondary-foreground{\r\n  color: hsl(var(--secondary-foreground));\r\n}\r\n.text-slate-100{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(241 245 249 / var(--tw-text-opacity, 1));\r\n}\r\n.text-success{\r\n  color: hsl(var(--success));\r\n}\r\n.text-success-foreground{\r\n  color: hsl(var(--success-foreground));\r\n}\r\n.text-success\\/70{\r\n  color: hsl(var(--success) / 0.7);\r\n}\r\n.text-teal-600{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(13 148 136 / var(--tw-text-opacity, 1));\r\n}\r\n.text-transparent{\r\n  color: transparent;\r\n}\r\n.text-warning{\r\n  color: hsl(var(--warning));\r\n}\r\n.text-warning-foreground{\r\n  color: hsl(var(--warning-foreground));\r\n}\r\n.text-white{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\r\n}\r\n.text-white\\/80{\r\n  color: rgb(255 255 255 / 0.8);\r\n}\r\n.text-yellow-400{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(250 204 21 / var(--tw-text-opacity, 1));\r\n}\r\n.text-yellow-500{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(234 179 8 / var(--tw-text-opacity, 1));\r\n}\r\n.text-yellow-600{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(202 138 4 / var(--tw-text-opacity, 1));\r\n}\r\n.text-yellow-800{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(133 77 14 / var(--tw-text-opacity, 1));\r\n}\r\n.underline{\r\n  text-decoration-line: underline;\r\n}\r\n.underline-offset-2{\r\n  text-underline-offset: 2px;\r\n}\r\n.underline-offset-4{\r\n  text-underline-offset: 4px;\r\n}\r\n.placeholder-gray-500::-moz-placeholder{\r\n  --tw-placeholder-opacity: 1;\r\n  color: rgb(107 114 128 / var(--tw-placeholder-opacity, 1));\r\n}\r\n.placeholder-gray-500::placeholder{\r\n  --tw-placeholder-opacity: 1;\r\n  color: rgb(107 114 128 / var(--tw-placeholder-opacity, 1));\r\n}\r\n.opacity-50{\r\n  opacity: 0.5;\r\n}\r\n.opacity-60{\r\n  opacity: 0.6;\r\n}\r\n.opacity-70{\r\n  opacity: 0.7;\r\n}\r\n.shadow{\r\n  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\r\n  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);\r\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\r\n}\r\n.shadow-glow{\r\n  --tw-shadow: 0 0 20px -2px rgba(0, 0, 0, 0.1);\r\n  --tw-shadow-colored: 0 0 20px -2px var(--tw-shadow-color);\r\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\r\n}\r\n.shadow-glow-primary{\r\n  --tw-shadow: 0 0 20px -2px hsl(var(--primary) / 0.3);\r\n  --tw-shadow-colored: 0 0 20px -2px var(--tw-shadow-color);\r\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\r\n}\r\n.shadow-lg{\r\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\r\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\r\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\r\n}\r\n.shadow-md{\r\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\r\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\r\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\r\n}\r\n.shadow-medium{\r\n  --tw-shadow: 0 4px 25px -2px rgba(0, 0, 0, 0.08), 0 10px 30px -2px rgba(0, 0, 0, 0.06);\r\n  --tw-shadow-colored: 0 4px 25px -2px var(--tw-shadow-color), 0 10px 30px -2px var(--tw-shadow-color);\r\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\r\n}\r\n.shadow-none{\r\n  --tw-shadow: 0 0 #0000;\r\n  --tw-shadow-colored: 0 0 #0000;\r\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\r\n}\r\n.shadow-sm{\r\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\r\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\r\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\r\n}\r\n.shadow-soft{\r\n  --tw-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);\r\n  --tw-shadow-colored: 0 2px 15px -3px var(--tw-shadow-color), 0 10px 20px -2px var(--tw-shadow-color);\r\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\r\n}\r\n.outline-none{\r\n  outline: 2px solid transparent;\r\n  outline-offset: 2px;\r\n}\r\n.outline{\r\n  outline-style: solid;\r\n}\r\n.outline-info{\r\n  outline-color: hsl(var(--info));\r\n}\r\n.outline-success{\r\n  outline-color: hsl(var(--success));\r\n}\r\n.outline-warning{\r\n  outline-color: hsl(var(--warning));\r\n}\r\n.ring{\r\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\r\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);\r\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\r\n}\r\n.ring-2{\r\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\r\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\r\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\r\n}\r\n.ring-blue-500{\r\n  --tw-ring-opacity: 1;\r\n  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));\r\n}\r\n.ring-green-500{\r\n  --tw-ring-opacity: 1;\r\n  --tw-ring-color: rgb(34 197 94 / var(--tw-ring-opacity, 1));\r\n}\r\n.ring-indigo-100{\r\n  --tw-ring-opacity: 1;\r\n  --tw-ring-color: rgb(224 231 255 / var(--tw-ring-opacity, 1));\r\n}\r\n.ring-offset-2{\r\n  --tw-ring-offset-width: 2px;\r\n}\r\n.ring-offset-background{\r\n  --tw-ring-offset-color: hsl(var(--background));\r\n}\r\n.filter{\r\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\r\n}\r\n.backdrop-blur-md{\r\n  --tw-backdrop-blur: blur(12px);\r\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\r\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\r\n}\r\n.backdrop-blur-sm{\r\n  --tw-backdrop-blur: blur(4px);\r\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\r\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\r\n}\r\n.transition{\r\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;\r\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\r\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;\r\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\r\n  transition-duration: 150ms;\r\n}\r\n.transition-all{\r\n  transition-property: all;\r\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\r\n  transition-duration: 150ms;\r\n}\r\n.transition-colors{\r\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\r\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\r\n  transition-duration: 150ms;\r\n}\r\n.transition-opacity{\r\n  transition-property: opacity;\r\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\r\n  transition-duration: 150ms;\r\n}\r\n.transition-shadow{\r\n  transition-property: box-shadow;\r\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\r\n  transition-duration: 150ms;\r\n}\r\n.transition-transform{\r\n  transition-property: transform;\r\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\r\n  transition-duration: 150ms;\r\n}\r\n.duration-200{\r\n  transition-duration: 200ms;\r\n}\r\n.duration-300{\r\n  transition-duration: 300ms;\r\n}\r\n.duration-500{\r\n  transition-duration: 500ms;\r\n}\r\n.ease-in-out{\r\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\r\n}\r\n@keyframes enter{\r\n\r\n  from{\r\n    opacity: var(--tw-enter-opacity, 1);\r\n    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));\r\n  }\r\n}\r\n@keyframes exit{\r\n\r\n  to{\r\n    opacity: var(--tw-exit-opacity, 1);\r\n    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));\r\n  }\r\n}\r\n.animate-in{\r\n  animation-name: enter;\r\n  animation-duration: 150ms;\r\n  --tw-enter-opacity: initial;\r\n  --tw-enter-scale: initial;\r\n  --tw-enter-rotate: initial;\r\n  --tw-enter-translate-x: initial;\r\n  --tw-enter-translate-y: initial;\r\n}\r\n.fade-in-0{\r\n  --tw-enter-opacity: 0;\r\n}\r\n.zoom-in-95{\r\n  --tw-enter-scale: .95;\r\n}\r\n.duration-200{\r\n  animation-duration: 200ms;\r\n}\r\n.duration-300{\r\n  animation-duration: 300ms;\r\n}\r\n.duration-500{\r\n  animation-duration: 500ms;\r\n}\r\n.ease-in-out{\r\n  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\r\n}\r\n.running{\r\n  animation-play-state: running;\r\n}\r\n/* Utilidades adicionales para tipografía */\r\n/* Animaciones mejoradas para skeleton */\r\n@keyframes shimmer {\r\n    0% {\r\n      background-position: -200% 0;\r\n    }\r\n    100% {\r\n      background-position: 200% 0;\r\n    }\r\n  }\r\n.animate-shimmer {\r\n    animation: shimmer 2s ease-in-out infinite;\r\n  }\r\n/* Animaciones de entrada sutiles */\r\n@keyframes fade-in {\r\n    0% {\r\n      opacity: 0;\r\n    }\r\n    100% {\r\n      opacity: 1;\r\n    }\r\n  }\r\n@keyframes scale-in {\r\n    0% {\r\n      opacity: 0;\r\n      transform: scale(0.95);\r\n    }\r\n    100% {\r\n      opacity: 1;\r\n      transform: scale(1);\r\n    }\r\n  }\r\n@keyframes slide-up {\r\n    0% {\r\n      opacity: 0;\r\n      transform: translateY(10px);\r\n    }\r\n    100% {\r\n      opacity: 1;\r\n      transform: translateY(0);\r\n    }\r\n  }\r\n/* Respect user's motion preferences */\r\n@media (prefers-reduced-motion: reduce) {\r\n    .animate-fade-in,\r\n    .animate-scale-in,\r\n    .animate-slide-up,\r\n    .rayuela-fade-in,\r\n    .rayuela-scale-in,\r\n    .rayuela-slide-up {\r\n      animation: none;\r\n    }\r\n\r\n    .animate-shimmer,\r\n    .rayuela-shimmer {\r\n      animation: none;\r\n      background: linear-gradient(90deg, transparent 0%, var(--muted) 50%, transparent 100%);\r\n    }\r\n  }\r\n/* Utilidades para el toque creativo de Rayuela */\r\n/* Utilidades para sombras mejoradas */\r\n.shadow-soft {\r\n    box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);\r\n  }\r\n.shadow-medium {\r\n    box-shadow: 0 4px 25px -2px rgba(0, 0, 0, 0.08), 0 10px 30px -2px rgba(0, 0, 0, 0.06);\r\n  }\r\n.shadow-glow {\r\n    box-shadow: 0 0 20px -2px rgba(0, 0, 0, 0.1);\r\n  }\r\n/* Estados de interacción específicos de Rayuela */\r\n.rayuela-focus-ring:focus-visible{\r\n  outline: 2px solid transparent;\r\n  outline-offset: 2px;\r\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\r\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\r\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\r\n  --tw-ring-color: hsl(var(--primary));\r\n  --tw-ring-offset-width: 2px;\r\n}\r\n.\\[i\\:i\\+self\\.batch_size\\]{\r\n  i: i+self.batch size;\r\n}\r\n\r\n/* === UTILIDADES DE ACCESIBILIDAD === */\r\n\r\n/* Screen reader only - oculta visualmente pero mantiene accesible para lectores de pantalla */\r\n.sr-only {\r\n  position: absolute;\r\n  width: 1px;\r\n  height: 1px;\r\n  padding: 0;\r\n  margin: -1px;\r\n  overflow: hidden;\r\n  clip: rect(0, 0, 0, 0);\r\n  white-space: nowrap;\r\n  border: 0;\r\n}\r\n\r\n/* Mostrar cuando recibe focus (útil para skip links) */\r\n.sr-only:focus {\r\n  position: static;\r\n  width: auto;\r\n  height: auto;\r\n  padding: inherit;\r\n  margin: inherit;\r\n  overflow: visible;\r\n  clip: auto;\r\n  white-space: normal;\r\n}\r\n.selection\\:bg-primary *::-moz-selection{\r\n  background-color: hsl(var(--primary));\r\n}\r\n.selection\\:bg-primary *::selection{\r\n  background-color: hsl(var(--primary));\r\n}\r\n.selection\\:text-primary-foreground *::-moz-selection{\r\n  color: hsl(var(--primary-foreground));\r\n}\r\n.selection\\:text-primary-foreground *::selection{\r\n  color: hsl(var(--primary-foreground));\r\n}\r\n.selection\\:bg-primary::-moz-selection{\r\n  background-color: hsl(var(--primary));\r\n}\r\n.selection\\:bg-primary::selection{\r\n  background-color: hsl(var(--primary));\r\n}\r\n.selection\\:text-primary-foreground::-moz-selection{\r\n  color: hsl(var(--primary-foreground));\r\n}\r\n.selection\\:text-primary-foreground::selection{\r\n  color: hsl(var(--primary-foreground));\r\n}\r\n.file\\:inline-flex::file-selector-button{\r\n  display: inline-flex;\r\n}\r\n.file\\:h-7::file-selector-button{\r\n  height: 1.75rem;\r\n}\r\n.file\\:border-0::file-selector-button{\r\n  border-width: 0px;\r\n}\r\n.file\\:bg-transparent::file-selector-button{\r\n  background-color: transparent;\r\n}\r\n.file\\:text-sm::file-selector-button{\r\n  font-size: 0.875rem;\r\n  line-height: 1.25rem;\r\n}\r\n.file\\:font-medium::file-selector-button{\r\n  font-weight: 500;\r\n}\r\n.file\\:text-foreground::file-selector-button{\r\n  color: hsl(var(--foreground));\r\n}\r\n.placeholder\\:text-muted-foreground::-moz-placeholder{\r\n  color: hsl(var(--muted-foreground));\r\n}\r\n.placeholder\\:text-muted-foreground::placeholder{\r\n  color: hsl(var(--muted-foreground));\r\n}\r\n.last\\:border-b-0:last-child{\r\n  border-bottom-width: 0px;\r\n}\r\n.focus-within\\:relative:focus-within{\r\n  position: relative;\r\n}\r\n.focus-within\\:z-20:focus-within{\r\n  z-index: 20;\r\n}\r\n.hover\\:scale-105:hover{\r\n  --tw-scale-x: 1.05;\r\n  --tw-scale-y: 1.05;\r\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\r\n}\r\n.hover\\:scale-\\[1\\.02\\]:hover{\r\n  --tw-scale-x: 1.02;\r\n  --tw-scale-y: 1.02;\r\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\r\n}\r\n.hover\\:border-blue-100:hover{\r\n  --tw-border-opacity: 1;\r\n  border-color: rgb(219 234 254 / var(--tw-border-opacity, 1));\r\n}\r\n.hover\\:border-border:hover{\r\n  border-color: hsl(var(--border));\r\n}\r\n.hover\\:border-green-100:hover{\r\n  --tw-border-opacity: 1;\r\n  border-color: rgb(220 252 231 / var(--tw-border-opacity, 1));\r\n}\r\n.hover\\:border-primary\\/50:hover{\r\n  border-color: hsl(var(--primary) / 0.5);\r\n}\r\n.hover\\:border-purple-100:hover{\r\n  --tw-border-opacity: 1;\r\n  border-color: rgb(243 232 255 / var(--tw-border-opacity, 1));\r\n}\r\n.hover\\:border-purple-200:hover{\r\n  --tw-border-opacity: 1;\r\n  border-color: rgb(233 213 255 / var(--tw-border-opacity, 1));\r\n}\r\n.hover\\:border-ring\\/50:hover{\r\n  border-color: hsl(var(--ring) / 0.5);\r\n}\r\n.hover\\:bg-accent:hover{\r\n  background-color: hsl(var(--accent));\r\n}\r\n.hover\\:bg-accent\\/50:hover{\r\n  background-color: hsl(var(--accent) / 0.5);\r\n}\r\n.hover\\:bg-accent\\/90:hover{\r\n  background-color: hsl(var(--accent) / 0.9);\r\n}\r\n.hover\\:bg-amber-100:hover{\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(254 243 199 / var(--tw-bg-opacity, 1));\r\n}\r\n.hover\\:bg-background\\/50:hover{\r\n  background-color: hsl(var(--background) / 0.5);\r\n}\r\n.hover\\:bg-blue-700:hover{\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));\r\n}\r\n.hover\\:bg-destructive\\/10:hover{\r\n  background-color: hsl(var(--destructive) / 0.1);\r\n}\r\n.hover\\:bg-destructive\\/25:hover{\r\n  background-color: hsl(var(--destructive) / 0.25);\r\n}\r\n.hover\\:bg-destructive\\/80:hover{\r\n  background-color: hsl(var(--destructive) / 0.8);\r\n}\r\n.hover\\:bg-destructive\\/90:hover{\r\n  background-color: hsl(var(--destructive) / 0.9);\r\n}\r\n.hover\\:bg-gray-600:hover{\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));\r\n}\r\n.hover\\:bg-gray-800:hover{\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));\r\n}\r\n.hover\\:bg-indigo-50:hover{\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(238 242 255 / var(--tw-bg-opacity, 1));\r\n}\r\n.hover\\:bg-info\\/10:hover{\r\n  background-color: hsl(var(--info) / 0.1);\r\n}\r\n.hover\\:bg-info\\/15:hover{\r\n  background-color: hsl(var(--info) / 0.15);\r\n}\r\n.hover\\:bg-info\\/25:hover{\r\n  background-color: hsl(var(--info) / 0.25);\r\n}\r\n.hover\\:bg-info\\/80:hover{\r\n  background-color: hsl(var(--info) / 0.8);\r\n}\r\n.hover\\:bg-muted:hover{\r\n  background-color: hsl(var(--muted));\r\n}\r\n.hover\\:bg-muted\\/30:hover{\r\n  background-color: hsl(var(--muted) / 0.3);\r\n}\r\n.hover\\:bg-muted\\/50:hover{\r\n  background-color: hsl(var(--muted) / 0.5);\r\n}\r\n.hover\\:bg-muted\\/70:hover{\r\n  background-color: hsl(var(--muted) / 0.7);\r\n}\r\n.hover\\:bg-primary:hover{\r\n  background-color: hsl(var(--primary));\r\n}\r\n.hover\\:bg-primary\\/80:hover{\r\n  background-color: hsl(var(--primary) / 0.8);\r\n}\r\n.hover\\:bg-primary\\/90:hover{\r\n  background-color: hsl(var(--primary) / 0.9);\r\n}\r\n.hover\\:bg-secondary\\/80:hover{\r\n  background-color: hsl(var(--secondary) / 0.8);\r\n}\r\n.hover\\:bg-success\\/10:hover{\r\n  background-color: hsl(var(--success) / 0.1);\r\n}\r\n.hover\\:bg-success\\/15:hover{\r\n  background-color: hsl(var(--success) / 0.15);\r\n}\r\n.hover\\:bg-success\\/25:hover{\r\n  background-color: hsl(var(--success) / 0.25);\r\n}\r\n.hover\\:bg-success\\/80:hover{\r\n  background-color: hsl(var(--success) / 0.8);\r\n}\r\n.hover\\:bg-success\\/90:hover{\r\n  background-color: hsl(var(--success) / 0.9);\r\n}\r\n.hover\\:bg-warning-light:hover{\r\n  background-color: hsl(var(--warning-light));\r\n}\r\n.hover\\:bg-warning\\/10:hover{\r\n  background-color: hsl(var(--warning) / 0.1);\r\n}\r\n.hover\\:bg-warning\\/15:hover{\r\n  background-color: hsl(var(--warning) / 0.15);\r\n}\r\n.hover\\:bg-warning\\/25:hover{\r\n  background-color: hsl(var(--warning) / 0.25);\r\n}\r\n.hover\\:bg-warning\\/80:hover{\r\n  background-color: hsl(var(--warning) / 0.8);\r\n}\r\n.hover\\:text-accent-foreground:hover{\r\n  color: hsl(var(--accent-foreground));\r\n}\r\n.hover\\:text-amber-700:hover{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(180 83 9 / var(--tw-text-opacity, 1));\r\n}\r\n.hover\\:text-amber-800:hover{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(146 64 14 / var(--tw-text-opacity, 1));\r\n}\r\n.hover\\:text-blue-700:hover{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(29 78 216 / var(--tw-text-opacity, 1));\r\n}\r\n.hover\\:text-blue-800:hover{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(30 64 175 / var(--tw-text-opacity, 1));\r\n}\r\n.hover\\:text-destructive:hover{\r\n  color: hsl(var(--destructive));\r\n}\r\n.hover\\:text-foreground:hover{\r\n  color: hsl(var(--foreground));\r\n}\r\n.hover\\:text-indigo-700:hover{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(67 56 202 / var(--tw-text-opacity, 1));\r\n}\r\n.hover\\:text-muted-foreground:hover{\r\n  color: hsl(var(--muted-foreground));\r\n}\r\n.hover\\:text-primary:hover{\r\n  color: hsl(var(--primary));\r\n}\r\n.hover\\:text-primary-foreground:hover{\r\n  color: hsl(var(--primary-foreground));\r\n}\r\n.hover\\:text-primary\\/80:hover{\r\n  color: hsl(var(--primary) / 0.8);\r\n}\r\n.hover\\:text-purple-600:hover{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(147 51 234 / var(--tw-text-opacity, 1));\r\n}\r\n.hover\\:text-purple-700:hover{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(126 34 206 / var(--tw-text-opacity, 1));\r\n}\r\n.hover\\:text-red-700:hover{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(185 28 28 / var(--tw-text-opacity, 1));\r\n}\r\n.hover\\:text-warning:hover{\r\n  color: hsl(var(--warning));\r\n}\r\n.hover\\:underline:hover{\r\n  text-decoration-line: underline;\r\n}\r\n.hover\\:opacity-100:hover{\r\n  opacity: 1;\r\n}\r\n.hover\\:opacity-80:hover{\r\n  opacity: 0.8;\r\n}\r\n.hover\\:opacity-90:hover{\r\n  opacity: 0.9;\r\n}\r\n.hover\\:shadow-lg:hover{\r\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\r\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\r\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\r\n}\r\n.hover\\:shadow-md:hover{\r\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\r\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\r\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\r\n}\r\n.hover\\:shadow-medium:hover{\r\n  --tw-shadow: 0 4px 25px -2px rgba(0, 0, 0, 0.08), 0 10px 30px -2px rgba(0, 0, 0, 0.06);\r\n  --tw-shadow-colored: 0 4px 25px -2px var(--tw-shadow-color), 0 10px 30px -2px var(--tw-shadow-color);\r\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\r\n}\r\n.hover\\:shadow-sm:hover{\r\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\r\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\r\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\r\n}\r\n.hover\\:shadow-xl:hover{\r\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\r\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\r\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\r\n}\r\n.hover\\:shadow-medium:hover {\r\n    box-shadow: 0 4px 25px -2px rgba(0, 0, 0, 0.08), 0 10px 30px -2px rgba(0, 0, 0, 0.06);\r\n  }\r\n.focus\\:top-6:focus{\r\n  top: 1.5rem;\r\n}\r\n.focus\\:rounded-lg:focus{\r\n  border-radius: var(--radius);\r\n}\r\n.focus\\:bg-accent:focus{\r\n  background-color: hsl(var(--accent));\r\n}\r\n.focus\\:bg-primary:focus{\r\n  background-color: hsl(var(--primary));\r\n}\r\n.focus\\:px-4:focus{\r\n  padding-left: 1rem;\r\n  padding-right: 1rem;\r\n}\r\n.focus\\:py-2:focus{\r\n  padding-top: 0.5rem;\r\n  padding-bottom: 0.5rem;\r\n}\r\n.focus\\:text-accent-foreground:focus{\r\n  color: hsl(var(--accent-foreground));\r\n}\r\n.focus\\:text-primary-foreground:focus{\r\n  color: hsl(var(--primary-foreground));\r\n}\r\n.focus\\:shadow-lg:focus{\r\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\r\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\r\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\r\n}\r\n.focus\\:outline-none:focus{\r\n  outline: 2px solid transparent;\r\n  outline-offset: 2px;\r\n}\r\n.focus\\:ring-2:focus{\r\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\r\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\r\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\r\n}\r\n.focus\\:ring-ring:focus{\r\n  --tw-ring-color: hsl(var(--ring));\r\n}\r\n.focus\\:ring-offset-2:focus{\r\n  --tw-ring-offset-width: 2px;\r\n}\r\n.focus-visible\\:outline-none:focus-visible{\r\n  outline: 2px solid transparent;\r\n  outline-offset: 2px;\r\n}\r\n.focus-visible\\:ring-2:focus-visible{\r\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\r\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\r\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\r\n}\r\n.focus-visible\\:ring-ring:focus-visible{\r\n  --tw-ring-color: hsl(var(--ring));\r\n}\r\n.focus-visible\\:ring-offset-2:focus-visible{\r\n  --tw-ring-offset-width: 2px;\r\n}\r\n.active\\:scale-95:active{\r\n  --tw-scale-x: .95;\r\n  --tw-scale-y: .95;\r\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\r\n}\r\n.active\\:bg-accent\\/80:active{\r\n  background-color: hsl(var(--accent) / 0.8);\r\n}\r\n.active\\:bg-destructive\\/90:active{\r\n  background-color: hsl(var(--destructive) / 0.9);\r\n}\r\n.active\\:bg-destructive\\/95:active{\r\n  background-color: hsl(var(--destructive) / 0.95);\r\n}\r\n.active\\:bg-info\\/25:active{\r\n  background-color: hsl(var(--info) / 0.25);\r\n}\r\n.active\\:bg-info\\/90:active{\r\n  background-color: hsl(var(--info) / 0.9);\r\n}\r\n.active\\:bg-primary\\/90:active{\r\n  background-color: hsl(var(--primary) / 0.9);\r\n}\r\n.active\\:bg-primary\\/95:active{\r\n  background-color: hsl(var(--primary) / 0.95);\r\n}\r\n.active\\:bg-secondary\\/90:active{\r\n  background-color: hsl(var(--secondary) / 0.9);\r\n}\r\n.active\\:bg-success\\/25:active{\r\n  background-color: hsl(var(--success) / 0.25);\r\n}\r\n.active\\:bg-success\\/90:active{\r\n  background-color: hsl(var(--success) / 0.9);\r\n}\r\n.active\\:bg-warning\\/25:active{\r\n  background-color: hsl(var(--warning) / 0.25);\r\n}\r\n.active\\:bg-warning\\/90:active{\r\n  background-color: hsl(var(--warning) / 0.9);\r\n}\r\n.active\\:text-primary\\/80:active{\r\n  color: hsl(var(--primary) / 0.8);\r\n}\r\n.disabled\\:pointer-events-none:disabled{\r\n  pointer-events: none;\r\n}\r\n.disabled\\:cursor-not-allowed:disabled{\r\n  cursor: not-allowed;\r\n}\r\n.disabled\\:opacity-50:disabled{\r\n  opacity: 0.5;\r\n}\r\n.group:hover .group-hover\\:translate-x-1{\r\n  --tw-translate-x: 0.25rem;\r\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\r\n}\r\n.group:hover .group-hover\\:scale-110{\r\n  --tw-scale-x: 1.1;\r\n  --tw-scale-y: 1.1;\r\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\r\n}\r\n.group:hover .group-hover\\:bg-gradient-to-br{\r\n  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));\r\n}\r\n.group:hover .group-hover\\:from-white{\r\n  --tw-gradient-from: #fff var(--tw-gradient-from-position);\r\n  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);\r\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\r\n}\r\n.group:hover .group-hover\\:to-purple-50{\r\n  --tw-gradient-to: #faf5ff var(--tw-gradient-to-position);\r\n}\r\n.group:hover .group-hover\\:text-foreground{\r\n  color: hsl(var(--foreground));\r\n}\r\n.group:hover .group-hover\\:text-green-700{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(21 128 61 / var(--tw-text-opacity, 1));\r\n}\r\n.group:hover .group-hover\\:text-muted-foreground{\r\n  color: hsl(var(--muted-foreground));\r\n}\r\n.group:hover .group-hover\\:text-purple-700{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(126 34 206 / var(--tw-text-opacity, 1));\r\n}\r\n.group:hover .group-hover\\:text-purple-900{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(88 28 135 / var(--tw-text-opacity, 1));\r\n}\r\n.peer:disabled ~ .peer-disabled\\:cursor-not-allowed{\r\n  cursor: not-allowed;\r\n}\r\n.peer:disabled ~ .peer-disabled\\:opacity-50{\r\n  opacity: 0.5;\r\n}\r\n.aria-selected\\:bg-accent[aria-selected=\"true\"]{\r\n  background-color: hsl(var(--accent));\r\n}\r\n.aria-selected\\:bg-primary[aria-selected=\"true\"]{\r\n  background-color: hsl(var(--primary));\r\n}\r\n.aria-selected\\:text-accent-foreground[aria-selected=\"true\"]{\r\n  color: hsl(var(--accent-foreground));\r\n}\r\n.aria-selected\\:text-muted-foreground[aria-selected=\"true\"]{\r\n  color: hsl(var(--muted-foreground));\r\n}\r\n.aria-selected\\:text-primary-foreground[aria-selected=\"true\"]{\r\n  color: hsl(var(--primary-foreground));\r\n}\r\n.aria-selected\\:opacity-100[aria-selected=\"true\"]{\r\n  opacity: 1;\r\n}\r\n.data-\\[disabled\\]\\:pointer-events-none[data-disabled]{\r\n  pointer-events: none;\r\n}\r\n.data-\\[side\\=bottom\\]\\:translate-y-1[data-side=\"bottom\"]{\r\n  --tw-translate-y: 0.25rem;\r\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\r\n}\r\n.data-\\[side\\=left\\]\\:-translate-x-1[data-side=\"left\"]{\r\n  --tw-translate-x: -0.25rem;\r\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\r\n}\r\n.data-\\[side\\=right\\]\\:translate-x-1[data-side=\"right\"]{\r\n  --tw-translate-x: 0.25rem;\r\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\r\n}\r\n.data-\\[side\\=top\\]\\:-translate-y-1[data-side=\"top\"]{\r\n  --tw-translate-y: -0.25rem;\r\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\r\n}\r\n@keyframes accordion-up{\r\n\r\n  from{\r\n    height: var(--radix-accordion-content-height);\r\n  }\r\n\r\n  to{\r\n    height: 0;\r\n  }\r\n}\r\n.data-\\[state\\=closed\\]\\:animate-accordion-up[data-state=\"closed\"]{\r\n  animation: accordion-up 0.2s ease-out;\r\n}\r\n@keyframes accordion-down{\r\n\r\n  from{\r\n    height: 0;\r\n  }\r\n\r\n  to{\r\n    height: var(--radix-accordion-content-height);\r\n  }\r\n}\r\n.data-\\[state\\=open\\]\\:animate-accordion-down[data-state=\"open\"]{\r\n  animation: accordion-down 0.2s ease-out;\r\n}\r\n.data-\\[state\\=active\\]\\:bg-background[data-state=\"active\"]{\r\n  background-color: hsl(var(--background));\r\n}\r\n.data-\\[state\\=active\\]\\:bg-blue-100[data-state=\"active\"]{\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));\r\n}\r\n.data-\\[state\\=active\\]\\:bg-green-100[data-state=\"active\"]{\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));\r\n}\r\n.data-\\[state\\=active\\]\\:bg-info\\/10[data-state=\"active\"]{\r\n  background-color: hsl(var(--info) / 0.1);\r\n}\r\n.data-\\[state\\=active\\]\\:bg-orange-100[data-state=\"active\"]{\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(255 237 213 / var(--tw-bg-opacity, 1));\r\n}\r\n.data-\\[state\\=active\\]\\:bg-purple-100[data-state=\"active\"]{\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));\r\n}\r\n.data-\\[state\\=active\\]\\:bg-success\\/10[data-state=\"active\"]{\r\n  background-color: hsl(var(--success) / 0.1);\r\n}\r\n.data-\\[state\\=checked\\]\\:bg-primary[data-state=\"checked\"]{\r\n  background-color: hsl(var(--primary));\r\n}\r\n.data-\\[state\\=open\\]\\:bg-accent[data-state=\"open\"]{\r\n  background-color: hsl(var(--accent));\r\n}\r\n.data-\\[state\\=selected\\]\\:bg-muted\\/50[data-state=\"selected\"]{\r\n  background-color: hsl(var(--muted) / 0.5);\r\n}\r\n.data-\\[state\\=active\\]\\:text-blue-700[data-state=\"active\"]{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(29 78 216 / var(--tw-text-opacity, 1));\r\n}\r\n.data-\\[state\\=active\\]\\:text-foreground[data-state=\"active\"]{\r\n  color: hsl(var(--foreground));\r\n}\r\n.data-\\[state\\=active\\]\\:text-green-700[data-state=\"active\"]{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(21 128 61 / var(--tw-text-opacity, 1));\r\n}\r\n.data-\\[state\\=active\\]\\:text-info[data-state=\"active\"]{\r\n  color: hsl(var(--info));\r\n}\r\n.data-\\[state\\=active\\]\\:text-orange-700[data-state=\"active\"]{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(194 65 12 / var(--tw-text-opacity, 1));\r\n}\r\n.data-\\[state\\=active\\]\\:text-purple-700[data-state=\"active\"]{\r\n  --tw-text-opacity: 1;\r\n  color: rgb(126 34 206 / var(--tw-text-opacity, 1));\r\n}\r\n.data-\\[state\\=active\\]\\:text-success[data-state=\"active\"]{\r\n  color: hsl(var(--success));\r\n}\r\n.data-\\[state\\=checked\\]\\:text-primary-foreground[data-state=\"checked\"]{\r\n  color: hsl(var(--primary-foreground));\r\n}\r\n.data-\\[state\\=open\\]\\:text-muted-foreground[data-state=\"open\"]{\r\n  color: hsl(var(--muted-foreground));\r\n}\r\n.data-\\[disabled\\]\\:opacity-50[data-disabled]{\r\n  opacity: 0.5;\r\n}\r\n.data-\\[state\\=active\\]\\:shadow-sm[data-state=\"active\"]{\r\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\r\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\r\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\r\n}\r\n.data-\\[state\\=selected\\]\\:shadow-sm[data-state=\"selected\"]{\r\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\r\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\r\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\r\n}\r\n.data-\\[state\\=open\\]\\:animate-in[data-state=\"open\"]{\r\n  animation-name: enter;\r\n  animation-duration: 150ms;\r\n  --tw-enter-opacity: initial;\r\n  --tw-enter-scale: initial;\r\n  --tw-enter-rotate: initial;\r\n  --tw-enter-translate-x: initial;\r\n  --tw-enter-translate-y: initial;\r\n}\r\n.data-\\[state\\=closed\\]\\:animate-out[data-state=\"closed\"]{\r\n  animation-name: exit;\r\n  animation-duration: 150ms;\r\n  --tw-exit-opacity: initial;\r\n  --tw-exit-scale: initial;\r\n  --tw-exit-rotate: initial;\r\n  --tw-exit-translate-x: initial;\r\n  --tw-exit-translate-y: initial;\r\n}\r\n.data-\\[state\\=closed\\]\\:fade-out-0[data-state=\"closed\"]{\r\n  --tw-exit-opacity: 0;\r\n}\r\n.data-\\[state\\=open\\]\\:fade-in-0[data-state=\"open\"]{\r\n  --tw-enter-opacity: 0;\r\n}\r\n.data-\\[state\\=closed\\]\\:zoom-out-95[data-state=\"closed\"]{\r\n  --tw-exit-scale: .95;\r\n}\r\n.data-\\[state\\=open\\]\\:zoom-in-95[data-state=\"open\"]{\r\n  --tw-enter-scale: .95;\r\n}\r\n.data-\\[side\\=bottom\\]\\:slide-in-from-top-2[data-side=\"bottom\"]{\r\n  --tw-enter-translate-y: -0.5rem;\r\n}\r\n.data-\\[side\\=left\\]\\:slide-in-from-right-2[data-side=\"left\"]{\r\n  --tw-enter-translate-x: 0.5rem;\r\n}\r\n.data-\\[side\\=right\\]\\:slide-in-from-left-2[data-side=\"right\"]{\r\n  --tw-enter-translate-x: -0.5rem;\r\n}\r\n.data-\\[side\\=top\\]\\:slide-in-from-bottom-2[data-side=\"top\"]{\r\n  --tw-enter-translate-y: 0.5rem;\r\n}\r\n.data-\\[state\\=closed\\]\\:slide-out-to-left-1\\/2[data-state=\"closed\"]{\r\n  --tw-exit-translate-x: -50%;\r\n}\r\n.data-\\[state\\=closed\\]\\:slide-out-to-top-\\[48\\%\\][data-state=\"closed\"]{\r\n  --tw-exit-translate-y: -48%;\r\n}\r\n.data-\\[state\\=open\\]\\:slide-in-from-left-1\\/2[data-state=\"open\"]{\r\n  --tw-enter-translate-x: -50%;\r\n}\r\n.data-\\[state\\=open\\]\\:slide-in-from-top-\\[48\\%\\][data-state=\"open\"]{\r\n  --tw-enter-translate-y: -48%;\r\n}\r\n.group[data-disabled=\"true\"] .group-data-\\[disabled\\=true\\]\\:pointer-events-none{\r\n  pointer-events: none;\r\n}\r\n.group[data-state=\"open\"] .group-data-\\[state\\=open\\]\\:rotate-180{\r\n  --tw-rotate: 180deg;\r\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\r\n}\r\n.group[data-disabled=\"true\"] .group-data-\\[disabled\\=true\\]\\:opacity-50{\r\n  opacity: 0.5;\r\n}\r\n.dark\\:border-blue-800:is(.dark *){\r\n  --tw-border-opacity: 1;\r\n  border-color: rgb(30 64 175 / var(--tw-border-opacity, 1));\r\n}\r\n.dark\\:border-destructive\\/40:is(.dark *){\r\n  border-color: hsl(var(--destructive) / 0.4);\r\n}\r\n.dark\\:border-gray-800:is(.dark *){\r\n  --tw-border-opacity: 1;\r\n  border-color: rgb(31 41 55 / var(--tw-border-opacity, 1));\r\n}\r\n.dark\\:border-green-800:is(.dark *){\r\n  --tw-border-opacity: 1;\r\n  border-color: rgb(22 101 52 / var(--tw-border-opacity, 1));\r\n}\r\n.dark\\:border-indigo-500:is(.dark *){\r\n  --tw-border-opacity: 1;\r\n  border-color: rgb(99 102 241 / var(--tw-border-opacity, 1));\r\n}\r\n.dark\\:border-indigo-800\\/40:is(.dark *){\r\n  border-color: rgb(55 48 163 / 0.4);\r\n}\r\n.dark\\:border-info\\/40:is(.dark *){\r\n  border-color: hsl(var(--info) / 0.4);\r\n}\r\n.dark\\:border-info\\/50:is(.dark *){\r\n  border-color: hsl(var(--info) / 0.5);\r\n}\r\n.dark\\:border-red-800:is(.dark *){\r\n  --tw-border-opacity: 1;\r\n  border-color: rgb(153 27 27 / var(--tw-border-opacity, 1));\r\n}\r\n.dark\\:border-success\\/40:is(.dark *){\r\n  border-color: hsl(var(--success) / 0.4);\r\n}\r\n.dark\\:border-success\\/50:is(.dark *){\r\n  border-color: hsl(var(--success) / 0.5);\r\n}\r\n.dark\\:border-warning\\/40:is(.dark *){\r\n  border-color: hsl(var(--warning) / 0.4);\r\n}\r\n.dark\\:border-warning\\/50:is(.dark *){\r\n  border-color: hsl(var(--warning) / 0.5);\r\n}\r\n.dark\\:bg-amber-900\\/50:is(.dark *){\r\n  background-color: rgb(120 53 15 / 0.5);\r\n}\r\n.dark\\:bg-blue-900:is(.dark *){\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(30 58 138 / var(--tw-bg-opacity, 1));\r\n}\r\n.dark\\:bg-blue-900\\/50:is(.dark *){\r\n  background-color: rgb(30 58 138 / 0.5);\r\n}\r\n.dark\\:bg-card\\/20:is(.dark *){\r\n  background-color: hsl(var(--card) / 0.2);\r\n}\r\n.dark\\:bg-destructive\\/20:is(.dark *){\r\n  background-color: hsl(var(--destructive) / 0.2);\r\n}\r\n.dark\\:bg-gray-700:is(.dark *){\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\r\n}\r\n.dark\\:bg-gray-800:is(.dark *){\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));\r\n}\r\n.dark\\:bg-gray-900:is(.dark *){\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));\r\n}\r\n.dark\\:bg-green-900:is(.dark *){\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(20 83 45 / var(--tw-bg-opacity, 1));\r\n}\r\n.dark\\:bg-green-900\\/20:is(.dark *){\r\n  background-color: rgb(20 83 45 / 0.2);\r\n}\r\n.dark\\:bg-indigo-800:is(.dark *){\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(55 48 163 / var(--tw-bg-opacity, 1));\r\n}\r\n.dark\\:bg-indigo-900\\/20:is(.dark *){\r\n  background-color: rgb(49 46 129 / 0.2);\r\n}\r\n.dark\\:bg-indigo-900\\/50:is(.dark *){\r\n  background-color: rgb(49 46 129 / 0.5);\r\n}\r\n.dark\\:bg-info\\/20:is(.dark *){\r\n  background-color: hsl(var(--info) / 0.2);\r\n}\r\n.dark\\:bg-input\\/30:is(.dark *){\r\n  background-color: hsl(var(--input) / 0.3);\r\n}\r\n.dark\\:bg-primary\\/20:is(.dark *){\r\n  background-color: hsl(var(--primary) / 0.2);\r\n}\r\n.dark\\:bg-purple-900\\/10:is(.dark *){\r\n  background-color: rgb(88 28 135 / 0.1);\r\n}\r\n.dark\\:bg-purple-900\\/20:is(.dark *){\r\n  background-color: rgb(88 28 135 / 0.2);\r\n}\r\n.dark\\:bg-purple-900\\/30:is(.dark *){\r\n  background-color: rgb(88 28 135 / 0.3);\r\n}\r\n.dark\\:bg-purple-900\\/50:is(.dark *){\r\n  background-color: rgb(88 28 135 / 0.5);\r\n}\r\n.dark\\:bg-red-900\\/20:is(.dark *){\r\n  background-color: rgb(127 29 29 / 0.2);\r\n}\r\n.dark\\:bg-slate-900:is(.dark *){\r\n  --tw-bg-opacity: 1;\r\n  background-color: rgb(15 23 42 / var(--tw-bg-opacity, 1));\r\n}\r\n.dark\\:bg-success\\/20:is(.dark *){\r\n  background-color: hsl(var(--success) / 0.2);\r\n}\r\n.dark\\:bg-warning\\/20:is(.dark *){\r\n  background-color: hsl(var(--warning) / 0.2);\r\n}\r\n.dark\\:from-blue-600:is(.dark *){\r\n  --tw-gradient-from: #2563eb var(--tw-gradient-from-position);\r\n  --tw-gradient-to: rgb(37 99 235 / 0) var(--tw-gradient-to-position);\r\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\r\n}\r\n.dark\\:from-blue-900\\/20:is(.dark *){\r\n  --tw-gradient-from: rgb(30 58 138 / 0.2) var(--tw-gradient-from-position);\r\n  --tw-gradient-to: rgb(30 58 138 / 0) var(--tw-gradient-to-position);\r\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\r\n}\r\n.dark\\:from-gray-500:is(.dark *){\r\n  --tw-gradient-from: #6b7280 var(--tw-gradient-from-position);\r\n  --tw-gradient-to: rgb(107 114 128 / 0) var(--tw-gradient-to-position);\r\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\r\n}\r\n.dark\\:from-gray-950:is(.dark *){\r\n  --tw-gradient-from: #030712 var(--tw-gradient-from-position);\r\n  --tw-gradient-to: rgb(3 7 18 / 0) var(--tw-gradient-to-position);\r\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\r\n}\r\n.dark\\:from-green-600:is(.dark *){\r\n  --tw-gradient-from: #16a34a var(--tw-gradient-from-position);\r\n  --tw-gradient-to: rgb(22 163 74 / 0) var(--tw-gradient-to-position);\r\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\r\n}\r\n.dark\\:from-indigo-900\\/10:is(.dark *){\r\n  --tw-gradient-from: rgb(49 46 129 / 0.1) var(--tw-gradient-from-position);\r\n  --tw-gradient-to: rgb(49 46 129 / 0) var(--tw-gradient-to-position);\r\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\r\n}\r\n.dark\\:to-emerald-600:is(.dark *){\r\n  --tw-gradient-to: #059669 var(--tw-gradient-to-position);\r\n}\r\n.dark\\:to-gray-600:is(.dark *){\r\n  --tw-gradient-to: #4b5563 var(--tw-gradient-to-position);\r\n}\r\n.dark\\:to-gray-900:is(.dark *){\r\n  --tw-gradient-to: #111827 var(--tw-gradient-to-position);\r\n}\r\n.dark\\:to-indigo-600:is(.dark *){\r\n  --tw-gradient-to: #4f46e5 var(--tw-gradient-to-position);\r\n}\r\n.dark\\:to-indigo-900\\/20:is(.dark *){\r\n  --tw-gradient-to: rgb(49 46 129 / 0.2) var(--tw-gradient-to-position);\r\n}\r\n.dark\\:to-transparent:is(.dark *){\r\n  --tw-gradient-to: transparent var(--tw-gradient-to-position);\r\n}\r\n.dark\\:text-amber-400:is(.dark *){\r\n  --tw-text-opacity: 1;\r\n  color: rgb(251 191 36 / var(--tw-text-opacity, 1));\r\n}\r\n.dark\\:text-blue-100:is(.dark *){\r\n  --tw-text-opacity: 1;\r\n  color: rgb(219 234 254 / var(--tw-text-opacity, 1));\r\n}\r\n.dark\\:text-blue-300:is(.dark *){\r\n  --tw-text-opacity: 1;\r\n  color: rgb(147 197 253 / var(--tw-text-opacity, 1));\r\n}\r\n.dark\\:text-blue-400:is(.dark *){\r\n  --tw-text-opacity: 1;\r\n  color: rgb(96 165 250 / var(--tw-text-opacity, 1));\r\n}\r\n.dark\\:text-destructive-foreground:is(.dark *){\r\n  color: hsl(var(--destructive-foreground));\r\n}\r\n.dark\\:text-gray-100:is(.dark *){\r\n  --tw-text-opacity: 1;\r\n  color: rgb(243 244 246 / var(--tw-text-opacity, 1));\r\n}\r\n.dark\\:text-gray-200:is(.dark *){\r\n  --tw-text-opacity: 1;\r\n  color: rgb(229 231 235 / var(--tw-text-opacity, 1));\r\n}\r\n.dark\\:text-green-200:is(.dark *){\r\n  --tw-text-opacity: 1;\r\n  color: rgb(187 247 208 / var(--tw-text-opacity, 1));\r\n}\r\n.dark\\:text-green-300:is(.dark *){\r\n  --tw-text-opacity: 1;\r\n  color: rgb(134 239 172 / var(--tw-text-opacity, 1));\r\n}\r\n.dark\\:text-green-400:is(.dark *){\r\n  --tw-text-opacity: 1;\r\n  color: rgb(74 222 128 / var(--tw-text-opacity, 1));\r\n}\r\n.dark\\:text-indigo-400:is(.dark *){\r\n  --tw-text-opacity: 1;\r\n  color: rgb(129 140 248 / var(--tw-text-opacity, 1));\r\n}\r\n.dark\\:text-info:is(.dark *){\r\n  color: hsl(var(--info));\r\n}\r\n.dark\\:text-info-foreground:is(.dark *){\r\n  color: hsl(var(--info-foreground));\r\n}\r\n.dark\\:text-muted-foreground\\/70:is(.dark *){\r\n  color: hsl(var(--muted-foreground) / 0.7);\r\n}\r\n.dark\\:text-purple-400:is(.dark *){\r\n  --tw-text-opacity: 1;\r\n  color: rgb(192 132 252 / var(--tw-text-opacity, 1));\r\n}\r\n.dark\\:text-red-300:is(.dark *){\r\n  --tw-text-opacity: 1;\r\n  color: rgb(252 165 165 / var(--tw-text-opacity, 1));\r\n}\r\n.dark\\:text-red-400:is(.dark *){\r\n  --tw-text-opacity: 1;\r\n  color: rgb(248 113 113 / var(--tw-text-opacity, 1));\r\n}\r\n.dark\\:text-success:is(.dark *){\r\n  color: hsl(var(--success));\r\n}\r\n.dark\\:text-success-foreground:is(.dark *){\r\n  color: hsl(var(--success-foreground));\r\n}\r\n.dark\\:text-warning:is(.dark *){\r\n  color: hsl(var(--warning));\r\n}\r\n.dark\\:text-warning-foreground:is(.dark *){\r\n  color: hsl(var(--warning-foreground));\r\n}\r\n.dark\\:text-yellow-400:is(.dark *){\r\n  --tw-text-opacity: 1;\r\n  color: rgb(250 204 21 / var(--tw-text-opacity, 1));\r\n}\r\n.dark\\:ring-indigo-900\\/20:is(.dark *){\r\n  --tw-ring-color: rgb(49 46 129 / 0.2);\r\n}\r\n.dark\\:hover\\:border-blue-900:hover:is(.dark *){\r\n  --tw-border-opacity: 1;\r\n  border-color: rgb(30 58 138 / var(--tw-border-opacity, 1));\r\n}\r\n.dark\\:hover\\:border-green-900:hover:is(.dark *){\r\n  --tw-border-opacity: 1;\r\n  border-color: rgb(20 83 45 / var(--tw-border-opacity, 1));\r\n}\r\n.dark\\:hover\\:border-purple-900:hover:is(.dark *){\r\n  --tw-border-opacity: 1;\r\n  border-color: rgb(88 28 135 / var(--tw-border-opacity, 1));\r\n}\r\n.dark\\:hover\\:bg-info\\/20:hover:is(.dark *){\r\n  background-color: hsl(var(--info) / 0.2);\r\n}\r\n.dark\\:hover\\:bg-success\\/20:hover:is(.dark *){\r\n  background-color: hsl(var(--success) / 0.2);\r\n}\r\n.dark\\:hover\\:bg-warning\\/20:hover:is(.dark *){\r\n  background-color: hsl(var(--warning) / 0.2);\r\n}\r\n.dark\\:hover\\:text-amber-300:hover:is(.dark *){\r\n  --tw-text-opacity: 1;\r\n  color: rgb(252 211 77 / var(--tw-text-opacity, 1));\r\n}\r\n.dark\\:hover\\:text-blue-300:hover:is(.dark *){\r\n  --tw-text-opacity: 1;\r\n  color: rgb(147 197 253 / var(--tw-text-opacity, 1));\r\n}\r\n.dark\\:hover\\:text-gray-200:hover:is(.dark *){\r\n  --tw-text-opacity: 1;\r\n  color: rgb(229 231 235 / var(--tw-text-opacity, 1));\r\n}\r\n.dark\\:hover\\:text-muted-foreground\\/50:hover:is(.dark *){\r\n  color: hsl(var(--muted-foreground) / 0.5);\r\n}\r\n.dark\\:hover\\:text-purple-300:hover:is(.dark *){\r\n  --tw-text-opacity: 1;\r\n  color: rgb(216 180 254 / var(--tw-text-opacity, 1));\r\n}\r\n.dark\\:hover\\:text-red-300:hover:is(.dark *){\r\n  --tw-text-opacity: 1;\r\n  color: rgb(252 165 165 / var(--tw-text-opacity, 1));\r\n}\r\n.dark\\:data-\\[state\\=active\\]\\:bg-blue-900\\/30[data-state=\"active\"]:is(.dark *){\r\n  background-color: rgb(30 58 138 / 0.3);\r\n}\r\n.dark\\:data-\\[state\\=active\\]\\:bg-green-900\\/30[data-state=\"active\"]:is(.dark *){\r\n  background-color: rgb(20 83 45 / 0.3);\r\n}\r\n.dark\\:data-\\[state\\=active\\]\\:bg-orange-900\\/30[data-state=\"active\"]:is(.dark *){\r\n  background-color: rgb(124 45 18 / 0.3);\r\n}\r\n.dark\\:data-\\[state\\=active\\]\\:bg-purple-900\\/30[data-state=\"active\"]:is(.dark *){\r\n  background-color: rgb(88 28 135 / 0.3);\r\n}\r\n.dark\\:data-\\[state\\=active\\]\\:text-blue-300[data-state=\"active\"]:is(.dark *){\r\n  --tw-text-opacity: 1;\r\n  color: rgb(147 197 253 / var(--tw-text-opacity, 1));\r\n}\r\n.dark\\:data-\\[state\\=active\\]\\:text-green-300[data-state=\"active\"]:is(.dark *){\r\n  --tw-text-opacity: 1;\r\n  color: rgb(134 239 172 / var(--tw-text-opacity, 1));\r\n}\r\n.dark\\:data-\\[state\\=active\\]\\:text-orange-300[data-state=\"active\"]:is(.dark *){\r\n  --tw-text-opacity: 1;\r\n  color: rgb(253 186 116 / var(--tw-text-opacity, 1));\r\n}\r\n.dark\\:data-\\[state\\=active\\]\\:text-purple-300[data-state=\"active\"]:is(.dark *){\r\n  --tw-text-opacity: 1;\r\n  color: rgb(216 180 254 / var(--tw-text-opacity, 1));\r\n}\r\n@media (min-width: 640px){\r\n\r\n  .sm\\:mt-0{\r\n    margin-top: 0px;\r\n  }\r\n\r\n  .sm\\:max-w-lg{\r\n    max-width: 32rem;\r\n  }\r\n\r\n  .sm\\:max-w-md{\r\n    max-width: 28rem;\r\n  }\r\n\r\n  .sm\\:max-w-2xl{\r\n    max-width: 42rem;\r\n  }\r\n\r\n  .sm\\:flex-row{\r\n    flex-direction: row;\r\n  }\r\n\r\n  .sm\\:items-center{\r\n    align-items: center;\r\n  }\r\n\r\n  .sm\\:justify-end{\r\n    justify-content: flex-end;\r\n  }\r\n\r\n  .sm\\:gap-2{\r\n    gap: 0.5rem;\r\n  }\r\n\r\n  .sm\\:gap-4{\r\n    gap: 1rem;\r\n  }\r\n\r\n  .sm\\:gap-6{\r\n    gap: 1.5rem;\r\n  }\r\n\r\n  .sm\\:space-x-2 > :not([hidden]) ~ :not([hidden]){\r\n    --tw-space-x-reverse: 0;\r\n    margin-right: calc(0.5rem * var(--tw-space-x-reverse));\r\n    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\r\n  }\r\n\r\n  .sm\\:rounded-lg{\r\n    border-radius: var(--radius);\r\n  }\r\n\r\n  .sm\\:text-left{\r\n    text-align: left;\r\n  }\r\n\r\n  .sm\\:text-base{\r\n    font-size: 1rem;\r\n    line-height: 1.5rem;\r\n  }\r\n\r\n  .sm\\:text-sm{\r\n    font-size: 0.875rem;\r\n    line-height: 1.25rem;\r\n  }\r\n}\r\n@media (min-width: 768px){\r\n\r\n  .md\\:order-1{\r\n    order: 1;\r\n  }\r\n\r\n  .md\\:order-2{\r\n    order: 2;\r\n  }\r\n\r\n  .md\\:col-span-1{\r\n    grid-column: span 1 / span 1;\r\n  }\r\n\r\n  .md\\:mb-0{\r\n    margin-bottom: 0px;\r\n  }\r\n\r\n  .md\\:block{\r\n    display: block;\r\n  }\r\n\r\n  .md\\:flex{\r\n    display: flex;\r\n  }\r\n\r\n  .md\\:hidden{\r\n    display: none;\r\n  }\r\n\r\n  .md\\:w-64{\r\n    width: 16rem;\r\n  }\r\n\r\n  .md\\:w-\\[200px\\]{\r\n    width: 200px;\r\n  }\r\n\r\n  .md\\:w-auto{\r\n    width: auto;\r\n  }\r\n\r\n  .md\\:grid-cols-2{\r\n    grid-template-columns: repeat(2, minmax(0, 1fr));\r\n  }\r\n\r\n  .md\\:grid-cols-3{\r\n    grid-template-columns: repeat(3, minmax(0, 1fr));\r\n  }\r\n\r\n  .md\\:grid-cols-4{\r\n    grid-template-columns: repeat(4, minmax(0, 1fr));\r\n  }\r\n\r\n  .md\\:flex-row{\r\n    flex-direction: row;\r\n  }\r\n\r\n  .md\\:gap-8{\r\n    gap: 2rem;\r\n  }\r\n\r\n  .md\\:p-8{\r\n    padding: 2rem;\r\n  }\r\n\r\n  .md\\:py-16{\r\n    padding-top: 4rem;\r\n    padding-bottom: 4rem;\r\n  }\r\n\r\n  .md\\:text-3xl{\r\n    font-size: 1.875rem;\r\n    line-height: 2.25rem;\r\n  }\r\n\r\n  .md\\:text-5xl{\r\n    font-size: 3rem;\r\n    line-height: 1;\r\n  }\r\n\r\n  .md\\:text-\\[1\\.1rem\\]{\r\n    font-size: 1.1rem;\r\n  }\r\n\r\n  .md\\:text-display-xl{\r\n    font-size: 3.75rem;\r\n    line-height: 1.2;\r\n    letter-spacing: -0.02em;\r\n  }\r\n\r\n  .md\\:text-sm{\r\n    font-size: 0.875rem;\r\n    line-height: 1.25rem;\r\n  }\r\n}\r\n@media (min-width: 1024px){\r\n\r\n  .lg\\:col-span-2{\r\n    grid-column: span 2 / span 2;\r\n  }\r\n\r\n  .lg\\:col-span-3{\r\n    grid-column: span 3 / span 3;\r\n  }\r\n\r\n  .lg\\:block{\r\n    display: block;\r\n  }\r\n\r\n  .lg\\:flex{\r\n    display: flex;\r\n  }\r\n\r\n  .lg\\:hidden{\r\n    display: none;\r\n  }\r\n\r\n  .lg\\:grid-cols-2{\r\n    grid-template-columns: repeat(2, minmax(0, 1fr));\r\n  }\r\n\r\n  .lg\\:grid-cols-3{\r\n    grid-template-columns: repeat(3, minmax(0, 1fr));\r\n  }\r\n\r\n  .lg\\:grid-cols-4{\r\n    grid-template-columns: repeat(4, minmax(0, 1fr));\r\n  }\r\n}\r\n@media (min-width: 1280px){\r\n\r\n  .xl\\:col-span-1{\r\n    grid-column: span 1 / span 1;\r\n  }\r\n\r\n  .xl\\:grid-cols-4{\r\n    grid-template-columns: repeat(4, minmax(0, 1fr));\r\n  }\r\n}\r\n.\\[\\&\\:has\\(\\>\\.day-range-end\\)\\]\\:rounded-r-md:has(>.day-range-end){\r\n  border-top-right-radius: calc(var(--radius) - 2px);\r\n  border-bottom-right-radius: calc(var(--radius) - 2px);\r\n}\r\n.\\[\\&\\:has\\(\\>\\.day-range-start\\)\\]\\:rounded-l-md:has(>.day-range-start){\r\n  border-top-left-radius: calc(var(--radius) - 2px);\r\n  border-bottom-left-radius: calc(var(--radius) - 2px);\r\n}\r\n.\\[\\&\\:has\\(\\[aria-selected\\]\\)\\]\\:rounded-md:has([aria-selected]){\r\n  border-radius: calc(var(--radius) - 2px);\r\n}\r\n.\\[\\&\\:has\\(\\[aria-selected\\]\\)\\]\\:bg-accent:has([aria-selected]){\r\n  background-color: hsl(var(--accent));\r\n}\r\n.first\\:\\[\\&\\:has\\(\\[aria-selected\\]\\)\\]\\:rounded-l-md:has([aria-selected]):first-child{\r\n  border-top-left-radius: calc(var(--radius) - 2px);\r\n  border-bottom-left-radius: calc(var(--radius) - 2px);\r\n}\r\n.last\\:\\[\\&\\:has\\(\\[aria-selected\\]\\)\\]\\:rounded-r-md:has([aria-selected]):last-child{\r\n  border-top-right-radius: calc(var(--radius) - 2px);\r\n  border-bottom-right-radius: calc(var(--radius) - 2px);\r\n}\r\n.\\[\\&\\:has\\(\\[aria-selected\\]\\.day-range-end\\)\\]\\:rounded-r-md:has([aria-selected].day-range-end){\r\n  border-top-right-radius: calc(var(--radius) - 2px);\r\n  border-bottom-right-radius: calc(var(--radius) - 2px);\r\n}\r\n.\\[\\&\\:has\\(\\[role\\=checkbox\\]\\)\\]\\:pr-0:has([role=checkbox]){\r\n  padding-right: 0px;\r\n}\r\n.\\[\\&\\>span\\]\\:line-clamp-1>span{\r\n  overflow: hidden;\r\n  display: -webkit-box;\r\n  -webkit-box-orient: vertical;\r\n  -webkit-line-clamp: 1;\r\n}\r\n.\\[\\&\\>svg\\+div\\]\\:translate-y-\\[-3px\\]>svg+div{\r\n  --tw-translate-y: -3px;\r\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\r\n}\r\n.\\[\\&\\>svg\\]\\:absolute>svg{\r\n  position: absolute;\r\n}\r\n.\\[\\&\\>svg\\]\\:left-4>svg{\r\n  left: 1rem;\r\n}\r\n.\\[\\&\\>svg\\]\\:top-4>svg{\r\n  top: 1rem;\r\n}\r\n.\\[\\&\\>svg\\]\\:text-destructive>svg{\r\n  color: hsl(var(--destructive));\r\n}\r\n.\\[\\&\\>svg\\]\\:text-foreground>svg{\r\n  color: hsl(var(--foreground));\r\n}\r\n.\\[\\&\\>svg\\]\\:text-info>svg{\r\n  color: hsl(var(--info));\r\n}\r\n.\\[\\&\\>svg\\]\\:text-success>svg{\r\n  color: hsl(var(--success));\r\n}\r\n.\\[\\&\\>svg\\]\\:text-warning>svg{\r\n  color: hsl(var(--warning));\r\n}\r\n.\\[\\&\\>svg\\~\\*\\]\\:pl-7>svg~*{\r\n  padding-left: 1.75rem;\r\n}\r\n.\\[\\&\\>tr\\]\\:last\\:border-b-0:last-child>tr{\r\n  border-bottom-width: 0px;\r\n}\r\n.\\[\\&\\[data-state\\=open\\]\\>svg\\]\\:rotate-180[data-state=open]>svg{\r\n  --tw-rotate: 180deg;\r\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\r\n}\r\n.\\[\\&_p\\]\\:leading-relaxed p{\r\n  line-height: 1.75;\r\n}\r\n.\\[\\&_svg\\:not\\(\\[class\\*\\=\\'size-\\'\\]\\)\\]\\:size-4 svg:not([class*='size-']){\r\n  width: 1rem;\r\n  height: 1rem;\r\n}\r\n.\\[\\&_svg\\]\\:pointer-events-none svg{\r\n  pointer-events: none;\r\n}\r\n.\\[\\&_svg\\]\\:shrink-0 svg{\r\n  flex-shrink: 0;\r\n}\r\n.\\[\\&_tr\\:last-child\\]\\:border-0 tr:last-child{\r\n  border-width: 0px;\r\n}\r\n.\\[\\&_tr\\]\\:border-b-0 tr{\r\n  border-bottom-width: 0px;\r\n}\r\n.\\[\\&_tr\\]\\:shadow-sm tr{\r\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\r\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\r\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\r\n}"], "names": [], "mappings": "AAIA;;AACA;;AACA;;AAKA;;AAEA;;;;AAwCA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2DA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4DA;;;;AA0BA;;;;;;;;;;;;;;AAeA;;;;;;;;;;;;AAWA;;;;;AA+BA;;;;AAQA;EACE;;;;;AAOF;;;;;AAMA;;;;;;;AAQA;;;;;;;;AAOA;;;;;AAMA;;;;;;;;;;;;;;;AAgBA;;;;;;;;;;;;AAWA;;;;;AAKA;;;;;;;;;;;;;AAYA;;;;;;AAOA;;;;;;AAOA;;;;AAQA;EACE;;;;;AAMF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuHA;;;;;AAUA;;;;AAiBA;;;;;;;;;;;;AAoBA;;;;;AAaA;;;;;;AAYA;;;;AAWA;;;;;AAgBA;;;;;AAWA;;;;AAcA;;;;;;;AAgBA;;;;AAUA;;;;;;;AASA;;;;AAKA;;;;AAYA;;;;;;AAcA;;;;;;;;;;;;;AAuBA;;;;AAYA;;;;;;AAeA;;;;AAUA;;;;AAUA;;;;AAUA;;;;AAYA;;;;;AAWA;;;;AAWA;;;;;AAWA;;;;AAUA;;;;AAiBA;;;;;AAMA;;;;AAKA;;;;;;AAcA;;;;AAUA;;;;AAWA;;;;;AAAA;;;;;AAkBA;;;;AAWA;;;;AAYA;;;;;AAkBA;;;;;AAUA;;;;AAKA;;;;;AAMA;;;;;;;;;;AAcA;;;;;AAIA;;;;;;;;AAOA;EAEE;;;;;AAKF;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;AAIA;;;;;AAIA;;;;;;;AAMA;;;;;;;;AAOA;;;;;;;;;AAQA;;;;;;;;;;AAYA;;;;;;;;;;;AAcA;;;;;;;;;AAQA;;;;;;;;;AAQA;;;;;;;;;AASA;;;;;;AAKA;;;;;;;;;AAQA;;;;AAGA;;;;;;;;;AAQA;;;;;;;;;AAQA;;;;;;;;;AAQA;;;;AAGA;;;;;AAIA;;;;AAIA;;;;;;;;;;AASA;;;;;AAKA;;;;AAIA;;;;;;;AAOA;;;;;;;AASA;;;;;;;;AASA;;;;;;;AAMA;;;;;;AAMA;;;;;;AAKA;;;;;;;AAOA;;;;;;AAMA;;;;;;;;AAOA;EACI;;;;;;;AAMJ;;;;;AAIA;;;;;;AAKA;;;;;;;AAMA;EACI;;;;;;;AAQJ;;;;AAGA;;;;AAGA;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAQA;EACI;;;;;;EAOA;;;;;;AAMJ;;;;;;AAWA;;;;;;;AAMA;;;;AAGA;;;;;;;AAMA;;;;AAGA;;;;;;;AAMA;;;;AA4BA;;;;;;;;;;;;;;;;AAoBA;;;;;AAIA;;;;;;;;;;;;;;;;;;;AAuBA;;;;;AAIA;;;;;;;;;;;;AAWA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAMA;;;;AAMA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;AAGA;;;;;;AAMA;;;;AAwnDA;;;;;;;;;;AAxmDA;;;;;;AAMA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAAA;;;;;AAQA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAKA;;;;;AAKA;;;;;;AAOA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;;;;AASA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAaA;;;;AAIA;;;;;;;;;;AAQA;;;;;;;;;;;;AAUA;;;;;;;;;;;;AAWA;EACI;;;;EASA;;;;;;AAQJ;;;;AAGA;;;;AAGA;;;;AAIA;;;;;;;;;;AASA;;;;AAOA;;;;;;;;;;;;AAaA;;;;;;;;;;;AAUA;;;;AAAA;;;;AAMA;;;;AAAA;;;;AAMA;;;;AAAA;;;;AAMA;;;;AAAA;;;;AAMA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAAA;;;;AAMA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;;AAKA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;;;;;AAUA;;;;AAGA;;;;;;;;;;AAUA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAUA;;;;;;;;;;AASA;;;;;;;;;;AASA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;EAEE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;;AAKF;EAEE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;;;EAMA;;;;;;AAKF;EAEE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;AAIF;EAEE;;;;EAIA;;;;;AAIF;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAQA;;;;AAGA;;;;;;;AAMA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA"}}]}