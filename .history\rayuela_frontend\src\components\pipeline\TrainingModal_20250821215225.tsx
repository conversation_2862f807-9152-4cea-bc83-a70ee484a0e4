"use client";

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  <PERSON><PERSON>Title,
  Di<PERSON>Trigger,
  DialogFooter
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Play, Brain, AlertCircle, CheckCircle, Loader2, Settings, ArrowRight } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';

interface TrainingModalProps {
  onTrainingStart: (parameters?: Record<string, unknown>) => Promise<unknown>;
  trigger?: React.ReactNode;
}

type ModelType = 'hybrid' | 'collaborative' | 'content';

export function TrainingModal({ onTrainingStart, trigger }: TrainingModalProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [modelType, setModelType] = useState<ModelType>('hybrid');
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [parameters, setParameters] = useState({
    learning_rate: 0.001,
    epochs: 50,
    batch_size: 32,
    embedding_dim: 64,
    regularization: 0.001
  });
  const [isTraining, setIsTraining] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [jobResult, setJobResult] = useState<any>(null);

  const handleParameterChange = (key: string, value: number) => {
    setParameters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleSubmit = async () => {
    setIsTraining(true);
    setError(null);

    try {
      // Structure the training parameters according to the backend API format
      const trainingRequest: Record<string, unknown> = {
        model_type: modelType,
        force: false
      };

      // Only include hyperparameters if advanced configuration is enabled
      if (showAdvanced) {
        trainingRequest.hyperparameters = {
          learning_rate: parameters.learning_rate,
          epochs: parameters.epochs,
          batch_size: parameters.batch_size,
          embedding_dim: parameters.embedding_dim,
          regularization: parameters.regularization
        };
      }

      const result = await onTrainingStart(trainingRequest);
      console.log('Training started:', result);
      setJobResult(result);
      setSuccess(true);

      // Reset form after 5 seconds to give user time to see job info
      setTimeout(() => {
        setIsOpen(false);
        setSuccess(false);
        setJobResult(null);
        setShowAdvanced(false);
        setModelType('hybrid');
      }, 5000);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error iniciando entrenamiento');
    } finally {
      setIsTraining(false);
    }
  };

  const handleClose = () => {
    if (!isTraining) {
      setIsOpen(false);
      setError(null);
      setSuccess(false);
      setJobResult(null);
      setShowAdvanced(false);
      setModelType('hybrid');
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button>
            <Play className="h-4 w-4 mr-2" />
            Nuevo Entrenamiento
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle>Nuevo Entrenamiento de Modelo</DialogTitle>
          <DialogDescription>
            Inicia el entrenamiento de un modelo de recomendación personalizado con tus datos
          </DialogDescription>
        </DialogHeader>

        {success ? (
          <div className="flex flex-col items-center py-6 space-y-4">
            <CheckCircle className="h-12 w-12 text-green-500 mb-2" />
            <div className="text-center space-y-2">
              <p className="text-lg font-semibold text-green-700">¡Entrenamiento iniciado!</p>
              <p className="text-sm text-muted-foreground">Tu modelo está siendo entrenado</p>
            </div>

            {jobResult && (
              <div className="w-full max-w-sm space-y-3">
                <div className="bg-muted/50 rounded-lg p-3 space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="font-medium">Job ID:</span>
                    <Badge variant="secondary">#{jobResult.jobId || jobResult.job_id}</Badge>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="font-medium">Estado:</span>
                    <Badge variant="outline">{jobResult.status || 'En cola'}</Badge>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="font-medium">Modelo:</span>
                    <Badge variant="outline">{modelType}</Badge>
                  </div>
                  {jobResult.message && (
                    <div className="text-xs text-muted-foreground">
                      {jobResult.message}
                    </div>
                  )}
                </div>

                <Button asChild variant="outline" size="sm" className="w-full">
                  <a href="/pipeline/training-jobs" target="_blank" rel="noopener noreferrer">
                    Ver en Historial de Entrenamientos
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </a>
                </Button>
              </div>
            )}
          </div>
        ) : (
          <div className="space-y-4">
            <div>
              <Label htmlFor="modelType">Tipo de modelo</Label>
              <Select value={modelType} onValueChange={(value: ModelType) => setModelType(value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecciona el tipo de modelo" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="hybrid">Híbrido (recomendado)</SelectItem>
                  <SelectItem value="collaborative">Filtrado colaborativo</SelectItem>
                  <SelectItem value="content">Basado en contenido</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground mt-1">
                {modelType === 'hybrid' && 'Combina múltiples técnicas para mejores resultados'}
                {modelType === 'collaborative' && 'Basado en comportamiento de usuarios similares'}
                {modelType === 'content' && 'Basado en características de productos'}
              </p>
            </div>

            <div className="flex items-center space-x-2">
              <input
                id="advanced"
                type="checkbox"
                className="rounded border-border"
                checked={showAdvanced}
                onChange={(e) => setShowAdvanced(e.target.checked)}
              />
              <Label htmlFor="advanced" className="flex items-center">
                <Settings className="h-4 w-4 mr-1" />
                Configuración avanzada
              </Label>
            </div>

            {showAdvanced && (
              <div className="space-y-3 p-4 border rounded-lg bg-muted/50">
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <Label htmlFor="learning_rate">Learning Rate</Label>
                    <Input
                      id="learning_rate"
                      type="number"
                      step="0.0001"
                      value={parameters.learning_rate}
                      onChange={(e) => handleParameterChange('learning_rate', parseFloat(e.target.value))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="epochs">Épocas</Label>
                    <Input
                      id="epochs"
                      type="number"
                      value={parameters.epochs}
                      onChange={(e) => handleParameterChange('epochs', parseInt(e.target.value))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="batch_size">Batch Size</Label>
                    <Input
                      id="batch_size"
                      type="number"
                      value={parameters.batch_size}
                      onChange={(e) => handleParameterChange('batch_size', parseInt(e.target.value))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="embedding_dim">Embedding Dim</Label>
                    <Input
                      id="embedding_dim"
                      type="number"
                      value={parameters.embedding_dim}
                      onChange={(e) => handleParameterChange('embedding_dim', parseInt(e.target.value))}
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="regularization">Regularización</Label>
                  <Input
                    id="regularization"
                    type="number"
                    step="0.0001"
                    value={parameters.regularization}
                    onChange={(e) => handleParameterChange('regularization', parseFloat(e.target.value))}
                  />
                </div>
              </div>
            )}

            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <div className="text-xs text-muted-foreground">
              <p>El entrenamiento puede tomar varios minutos dependiendo del volumen de datos</p>
              <p>Se requiere un mínimo de 100 interacciones para entrenar un modelo</p>
            </div>
          </div>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={isTraining}>
            Cancelar
          </Button>
          {!success && (
            <Button onClick={handleSubmit} disabled={isTraining}>
              {isTraining ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Entrenando...
                </>
              ) : (
                <>
                  <Brain className="h-4 w-4 mr-2" />
                  Iniciar Entrenamiento
                </>
              )}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 