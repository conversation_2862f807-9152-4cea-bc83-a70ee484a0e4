#!/usr/bin/env python3
"""
Script para migrar métricas de uso de Subscription a AccountUsageMetrics.

Este script ejecuta la migración de datos para consolidar las métricas de uso
y eliminar redundancia entre las tablas.

Uso:
    python scripts/migrate_usage_metrics.py [--verify-only] [--cleanup]
"""

import asyncio
import argparse
import sys
import os
from pathlib import Path

# Agregar src al path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.db.session import get_async_session
from src.services.usage_metrics_migration_service import UsageMetricsMigrationService
from src.utils.base_logger import log_info, log_error, log_warning


async def main():
    """Función principal del script de migración."""
    parser = argparse.ArgumentParser(description="Migrate usage metrics from Subscription to AccountUsageMetrics")
    parser.add_argument("--verify-only", action="store_true", help="Only verify migration status, don't migrate")
    parser.add_argument("--cleanup", action="store_true", help="Clean up deprecated fields after verification")
    parser.add_argument("--force-cleanup", action="store_true", help="Force cleanup without verification (DANGEROUS)")
    
    args = parser.parse_args()
    
    try:
        # Obtener sesión de base de datos
        async with get_async_session() as db:
            migration_service = UsageMetricsMigrationService(db)
            
            if args.verify_only:
                # Solo verificar estado de migración
                log_info("🔍 Verifying migration status...")
                verification = await migration_service.verify_migration()
                
                print("\n" + "="*60)
                print("📊 MIGRATION VERIFICATION REPORT")
                print("="*60)
                print(f"Subscriptions with metrics: {verification['subscriptions_with_metrics']}")
                print(f"Usage metrics records: {verification['usage_metrics_records']}")
                print(f"Inconsistencies found: {verification['inconsistencies_found']}")
                print(f"Migration complete: {'✅ YES' if verification['migration_complete'] else '❌ NO'}")
                
                if verification['inconsistencies']:
                    print("\n🚨 INCONSISTENCIES FOUND:")
                    for inconsistency in verification['inconsistencies']:
                        print(f"  - Account {inconsistency['account_id']}: {inconsistency['issue']}")
                
                return 0 if verification['migration_complete'] else 1
            
            elif args.cleanup or args.force_cleanup:
                # Limpiar campos deprecados
                if args.force_cleanup:
                    log_warning("⚠️ FORCE CLEANUP MODE - Skipping verification!")
                    cleanup_stats = await migration_service.cleanup_deprecated_fields()
                else:
                    log_info("🧹 Cleaning up deprecated fields...")
                    cleanup_stats = await migration_service.cleanup_deprecated_fields()
                
                print("\n" + "="*60)
                print("🧹 CLEANUP REPORT")
                print("="*60)
                print(f"Subscriptions cleaned: {cleanup_stats['subscriptions_cleaned']}")
                print(f"Cleanup completed: {'✅ YES' if cleanup_stats['cleanup_completed'] else '❌ NO'}")
                
                return 0
            
            else:
                # Ejecutar migración completa
                log_info("🚀 Starting usage metrics migration...")
                
                # Ejecutar migración
                migration_stats = await migration_service.migrate_subscription_metrics_to_usage_metrics()
                
                print("\n" + "="*60)
                print("📊 MIGRATION REPORT")
                print("="*60)
                print(f"Accounts processed: {migration_stats['accounts_processed']}")
                print(f"Accounts migrated: {migration_stats['accounts_migrated']}")
                print(f"Accounts updated: {migration_stats['accounts_updated']}")
                print(f"Accounts skipped: {migration_stats['accounts_skipped']}")
                print(f"Errors: {len(migration_stats['errors'])}")
                
                if migration_stats['errors']:
                    print("\n❌ ERRORS:")
                    for error in migration_stats['errors']:
                        print(f"  - {error}")
                
                # Verificar migración
                log_info("🔍 Verifying migration...")
                verification = await migration_service.verify_migration()
                
                print("\n" + "="*60)
                print("✅ VERIFICATION REPORT")
                print("="*60)
                print(f"Migration successful: {'✅ YES' if verification['migration_complete'] else '❌ NO'}")
                print(f"Inconsistencies: {verification['inconsistencies_found']}")
                
                if verification['inconsistencies']:
                    print("\n🚨 REMAINING INCONSISTENCIES:")
                    for inconsistency in verification['inconsistencies']:
                        print(f"  - Account {inconsistency['account_id']}: {inconsistency['issue']}")
                
                # Mostrar próximos pasos
                print("\n" + "="*60)
                print("📋 NEXT STEPS")
                print("="*60)
                if verification['migration_complete']:
                    print("1. ✅ Migration completed successfully")
                    print("2. 🔄 Update application code to use AccountUsageMetrics")
                    print("3. 🧪 Test thoroughly in staging environment")
                    print("4. 🧹 Run cleanup when ready: --cleanup")
                else:
                    print("1. ❌ Fix inconsistencies found above")
                    print("2. 🔄 Re-run migration")
                    print("3. 🔍 Verify again")
                
                return 0 if verification['migration_complete'] else 1
    
    except Exception as e:
        log_error(f"Migration failed: {str(e)}")
        print(f"\n❌ MIGRATION FAILED: {str(e)}")
        return 1


def print_usage_guide():
    """Imprime guía de uso del script."""
    print("""
🔄 USAGE METRICS MIGRATION GUIDE
================================

This script migrates usage metrics from Subscription to AccountUsageMetrics
to eliminate redundancy and improve data consistency.

COMMANDS:
---------
1. Run migration:
   python scripts/migrate_usage_metrics.py

2. Verify migration status:
   python scripts/migrate_usage_metrics.py --verify-only

3. Cleanup deprecated fields (after verification):
   python scripts/migrate_usage_metrics.py --cleanup

4. Force cleanup (DANGEROUS - skips verification):
   python scripts/migrate_usage_metrics.py --force-cleanup

MIGRATION PROCESS:
------------------
1. 📊 Migrate data from Subscription to AccountUsageMetrics
2. 🔍 Verify migration completeness
3. 🧹 Clean up deprecated fields (optional)

SAFETY NOTES:
-------------
- Migration is safe and non-destructive
- Cleanup is irreversible - only run after thorough testing
- Always verify migration before cleanup
- Test in staging environment first

ROLLBACK:
---------
If issues occur, you can restore from the deprecated fields
before running cleanup.
""")


if __name__ == "__main__":
    # Mostrar guía si no hay argumentos
    if len(sys.argv) == 1:
        print_usage_guide()
        sys.exit(0)
    
    # Ejecutar migración
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
