"""
Modelo para experimentos A/B de recomendaciones.
"""

from sqlalchemy import Column, Integer, String, Float, <PERSON>olean, DateTime, Text, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from src.db.base import Base, TenantMixin
from src.db.constants import ACCOUNT_ID_FK
import uuid
from enum import Enum


class ExperimentStatus(str, Enum):
    """Estados de un experimento."""
    DRAFT = "draft"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class ExperimentType(str, Enum):
    """Tipos de experimento."""
    RECOMMENDATION_STRATEGY = "recommendation_strategy"
    BASELINE_COMPARISON = "baseline_comparison"
    ALGORITHM_COMPARISON = "algorithm_comparison"
    UI_LAYOUT = "ui_layout"


class Experiment(Base, TenantMixin):
    """
    Modelo para experimentos A/B de recomendaciones.
    
    Permite comparar diferentes estrategias de recomendación,
    algoritmos, o configuraciones contra un baseline.
    """
    __tablename__ = "experiments"

    account_id = Column(Integer, ForeignKey(ACCOUNT_ID_FK), primary_key=True)
    experiment_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Información básica del experimento
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    experiment_type = Column(String(50), nullable=False, default=ExperimentType.BASELINE_COMPARISON)
    status = Column(String(20), nullable=False, default=ExperimentStatus.DRAFT)
    
    # Configuración del experimento
    traffic_allocation = Column(Float, nullable=False, default=0.5)  # % de tráfico para tratamiento
    control_config = Column(JSON, nullable=False)  # Configuración del grupo control
    treatment_config = Column(JSON, nullable=False)  # Configuración del grupo tratamiento
    
    # Criterios de éxito
    primary_metric = Column(String(50), nullable=False, default="ctr")  # ctr, cvr, revenue
    minimum_sample_size = Column(Integer, nullable=False, default=1000)
    minimum_effect_size = Column(Float, nullable=False, default=0.05)  # 5% mínimo de mejora
    confidence_level = Column(Float, nullable=False, default=0.95)  # 95% de confianza
    
    # Fechas
    start_date = Column(DateTime(timezone=True), nullable=True)
    end_date = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), default=func.now(), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), default=func.now(), onupdate=func.now())
    
    # Metadatos
    created_by = Column(String(255), nullable=True)
    tags = Column(JSON, nullable=True)  # Para categorización
    
    # Relaciones
    experiment_results = relationship("ExperimentResult", back_populates="experiment", cascade="all, delete-orphan")
    experiment_assignments = relationship("ExperimentAssignment", back_populates="experiment", cascade="all, delete-orphan")


class ExperimentAssignment(Base, TenantMixin):
    """
    Asignación de usuarios a grupos de experimento.
    
    Mantiene consistencia: un usuario siempre ve la misma variante
    durante todo el experimento.
    """
    __tablename__ = "experiment_assignments"

    account_id = Column(Integer, ForeignKey(ACCOUNT_ID_FK), primary_key=True)
    experiment_id = Column(UUID(as_uuid=True), ForeignKey("experiments.experiment_id"), primary_key=True)
    user_external_id = Column(String(255), primary_key=True)
    
    # Asignación
    variant = Column(String(20), nullable=False)  # "control" o "treatment"
    assigned_at = Column(DateTime(timezone=True), default=func.now(), server_default=func.now())
    
    # Metadatos de asignación
    assignment_method = Column(String(50), nullable=False, default="hash_based")  # hash_based, random
    hash_value = Column(Float, nullable=True)  # Para reproducibilidad
    
    # Relaciones
    experiment = relationship("Experiment", back_populates="experiment_assignments")


class ExperimentResult(Base, TenantMixin):
    """
    Resultados agregados de un experimento.
    
    Se actualiza periódicamente con las métricas calculadas
    para cada grupo del experimento.
    """
    __tablename__ = "experiment_results"

    account_id = Column(Integer, ForeignKey(ACCOUNT_ID_FK), primary_key=True)
    experiment_id = Column(UUID(as_uuid=True), ForeignKey("experiments.experiment_id"), primary_key=True)
    variant = Column(String(20), primary_key=True)  # "control" o "treatment"
    calculated_at = Column(DateTime(timezone=True), primary_key=True, default=func.now())
    
    # Métricas de muestra
    total_users = Column(Integer, nullable=False, default=0)
    total_impressions = Column(Integer, nullable=False, default=0)
    total_clicks = Column(Integer, nullable=False, default=0)
    total_conversions = Column(Integer, nullable=False, default=0)
    total_revenue = Column(Float, nullable=False, default=0.0)
    
    # Métricas calculadas
    ctr = Column(Float, nullable=False, default=0.0)
    cvr = Column(Float, nullable=False, default=0.0)
    revenue_per_user = Column(Float, nullable=False, default=0.0)
    avg_order_value = Column(Float, nullable=False, default=0.0)
    
    # Métricas de confianza estadística
    ctr_confidence_interval_lower = Column(Float, nullable=True)
    ctr_confidence_interval_upper = Column(Float, nullable=True)
    cvr_confidence_interval_lower = Column(Float, nullable=True)
    cvr_confidence_interval_upper = Column(Float, nullable=True)
    
    # Significancia estadística
    is_statistically_significant = Column(Boolean, nullable=False, default=False)
    p_value = Column(Float, nullable=True)
    effect_size = Column(Float, nullable=True)  # Diferencia vs control
    
    # Metadatos adicionales
    additional_metrics = Column(JSON, nullable=True)  # Para métricas específicas del experimento
    
    # Relaciones
    experiment = relationship("Experiment", back_populates="experiment_results")


class ExperimentEvent(Base, TenantMixin):
    """
    Eventos individuales del experimento.
    
    Registra cada interacción de usuario en el contexto del experimento
    para análisis detallado.
    """
    __tablename__ = "experiment_events"

    account_id = Column(Integer, ForeignKey(ACCOUNT_ID_FK), primary_key=True)
    event_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    experiment_id = Column(UUID(as_uuid=True), ForeignKey("experiments.experiment_id"), nullable=False)
    
    # Información del evento
    user_external_id = Column(String(255), nullable=False)
    variant = Column(String(20), nullable=False)  # "control" o "treatment"
    event_type = Column(String(50), nullable=False)  # "impression", "click", "conversion"
    
    # Contexto del evento
    product_external_id = Column(String(255), nullable=True)
    recommendation_position = Column(Integer, nullable=True)  # Posición en la lista
    page_context = Column(String(100), nullable=True)  # homepage, product, cart, etc.
    
    # Valor del evento
    event_value = Column(Float, nullable=True)  # Para conversiones con valor monetario
    
    # Metadatos
    timestamp = Column(DateTime(timezone=True), default=func.now(), server_default=func.now())
    session_id = Column(String(255), nullable=True)
    user_agent = Column(String(500), nullable=True)
    additional_context = Column(JSON, nullable=True)
    
    # Índices para consultas eficientes
    __table_args__ = (
        {"schema": None}  # Usar schema por defecto
    )
